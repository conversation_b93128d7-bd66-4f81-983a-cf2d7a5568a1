package cmd

import (
	"context"
	"fmt"

	"github.com/spf13/cobra"
	tea "github.com/charmbracelet/bubbletea"

	"arien-ai/internal/ai/function_calling"
	"arien-ai/internal/ai/providers"
	"arien-ai/internal/config"
	"arien-ai/internal/shell"
	"arien-ai/internal/ui/models"
)

// chatCmd represents the chat command
var chatCmd = &cobra.Command{
	Use:   "chat",
	Short: "Start an interactive chat session with AI",
	Long: `Start an interactive chat session with AI that can execute commands and complete tasks.

The chat interface provides:
- Real-time AI responses with streaming
- Function calling for command execution
- Safety checks and confirmation prompts
- Command history and context awareness
- Modern terminal UI with animations

Examples:
  arien-ai chat                    # Start interactive chat
  arien-ai chat --provider ollama  # Use Ollama provider
  arien-ai chat --model deepseek-reasoner  # Use specific model`,
	RunE: runChat,
}

func init() {
	rootCmd.AddCommand(chatCmd)
	
	// Chat-specific flags
	chatCmd.Flags().Bool("stream", true, "Enable streaming responses")
	chatCmd.Flags().Bool("function-calling", true, "Enable function calling")
	chatCmd.Flags().String("system-prompt", "", "Custom system prompt")
	chatCmd.Flags().Int("max-tokens", 4000, "Maximum tokens in response")
	chatCmd.Flags().Float64("temperature", 0.7, "Response temperature (0.0-2.0)")
}

func runChat(cmd *cobra.Command, args []string) error {
	// Get configuration
	if cfg == nil {
		return fmt.Errorf("configuration not loaded")
	}
	
	// Get flags
	providerName, _ := cmd.Flags().GetString("provider")
	modelName, _ := cmd.Flags().GetString("model")
	stream, _ := cmd.Flags().GetBool("stream")
	functionCalling, _ := cmd.Flags().GetBool("function-calling")
	systemPrompt, _ := cmd.Flags().GetString("system-prompt")
	maxTokens, _ := cmd.Flags().GetInt("max-tokens")
	temperature, _ := cmd.Flags().GetFloat64("temperature")
	debug, _ := cmd.Flags().GetBool("debug")
	
	// Create AI provider
	var provider providers.AIProvider
	
	switch providerName {
	case "deepseek":
		providerConfig := providers.ProviderConfig{
			Name:       "deepseek",
			BaseURL:    cfg.AI.Providers.Deepseek.BaseURL,
			APIKey:     cfg.AI.Providers.Deepseek.APIKey,
			Timeout:    cfg.AI.Providers.Deepseek.Timeout,
			MaxRetries: cfg.AI.Providers.Deepseek.MaxRetries,
			Headers:    cfg.AI.Providers.Deepseek.Headers,
		}
		provider = providers.NewDeepseekProvider(providerConfig)
		
		if modelName == "" {
			modelName = cfg.AI.Providers.Deepseek.Models.Chat
		}
		
	case "ollama":
		providerConfig := providers.ProviderConfig{
			Name:       "ollama",
			BaseURL:    cfg.AI.Providers.Ollama.BaseURL,
			Timeout:    cfg.AI.Providers.Ollama.Timeout,
			MaxRetries: cfg.AI.Providers.Ollama.MaxRetries,
			Headers:    cfg.AI.Providers.Ollama.Headers,
		}
		provider = providers.NewOllamaProvider(providerConfig)

		if modelName == "" {
			modelName = cfg.AI.Providers.Ollama.Models.Default
		}
		
	default:
		return fmt.Errorf("unsupported provider: %s", providerName)
	}
	
	// Test provider health
	ctx := context.Background()
	if err := provider.IsHealthy(ctx); err != nil {
		return fmt.Errorf("provider health check failed: %w", err)
	}
	
	// Create shell executor
	shellConfig := shell.GetDefaultConfig()
	shellConfig.RequireConfirmation = cfg.Security.RequireConfirmation
	shellConfig.DangerousCommands = cfg.Security.DangerousCommands
	shellConfig.MaxExecutionTime = cfg.Security.MaxExecutionTime
	shellConfig.SandboxMode = cfg.Security.SandboxMode
	shellConfig.AllowedDirectories = cfg.Security.AllowedDirectories
	
	executor := shell.NewExecutor(shellConfig)
	
	// Create function calling registry
	registry := function_calling.NewRegistry()
	
	// Register tools if function calling is enabled
	if functionCalling && provider.SupportsFunctionCalling() {
		if err := registerTools(registry, executor); err != nil {
			return fmt.Errorf("failed to register tools: %w", err)
		}
	}
	
	// Create chat model configuration
	chatConfig := models.ChatConfig{
		Provider:        provider,
		Model:          modelName,
		Stream:         stream,
		FunctionCalling: functionCalling,
		SystemPrompt:   systemPrompt,
		MaxTokens:      maxTokens,
		Temperature:    temperature,
		Debug:          debug,
		Registry:       registry,
		Executor:       executor,
		UIConfig:       cfg.UI,
	}
	
	// Create and run the chat model
	chatModel := models.NewChatModel(chatConfig)
	
	program := tea.NewProgram(
		chatModel,
		tea.WithAltScreen(),
		tea.WithMouseCellMotion(),
	)
	
	if _, err := program.Run(); err != nil {
		return fmt.Errorf("failed to run chat interface: %w", err)
	}
	
	return nil
}

// registerTools registers all available tools with the function calling registry
func registerTools(registry *function_calling.Registry, executor *shell.Executor) error {
	return registry.RegisterDefaultTools(executor)
}

