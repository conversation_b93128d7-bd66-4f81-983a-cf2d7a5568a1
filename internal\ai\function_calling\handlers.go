package function_calling

import (
	"context"
	"fmt"
	"os"
	"runtime"
	"strings"
	"time"

	"arien-ai/internal/shell"
	"arien-ai/pkg/utils"
)

// CreateShellExecuteHandler creates a handler for shell command execution
func CreateShellExecuteHandler(executor *shell.Executor) ToolHandler {
	return func(ctx context.Context, params map[string]interface{}) (*ToolResult, error) {
		// Extract parameters
		command, ok := params["command"].(string)
		if !ok || command == "" {
			return &ToolResult{
				Success: false,
				Error:   "command parameter is required and must be a string",
			}, nil
		}

		// Optional parameters
		workingDir := utils.SafeString(params["working_directory"])
		timeout := time.Duration(utils.SafeInt(params["timeout"])) * time.Second
		if timeout == 0 {
			timeout = 30 * time.Second
		}
		captureOutput := utils.SafeBool(params["capture_output"])
		if _, exists := params["capture_output"]; !exists {
			captureOutput = true // Default to true
		}

		// Environment variables
		var environment map[string]string
		if env, ok := params["environment"].(map[string]interface{}); ok {
			environment = make(map[string]string)
			for k, v := range env {
				environment[k] = utils.SafeString(v)
			}
		}

		// Create execution request
		request := shell.ExecutionRequest{
			Command:          command,
			WorkingDirectory: workingDir,
			Timeout:          timeout,
			CaptureOutput:    captureOutput,
			Environment:      environment,
			RequireConfirm:   false, // Handled at higher level
		}

		// Execute command
		result, err := executor.Execute(ctx, request)
		if err != nil {
			return &ToolResult{
				Success: false,
				Error:   fmt.Sprintf("execution failed: %v", err),
				Metadata: map[string]interface{}{
					"command":     command,
					"working_dir": workingDir,
					"timeout":     timeout.String(),
				},
			}, nil
		}

		// Format output
		output := result.Stdout
		if result.Stderr != "" {
			if output != "" {
				output += "\n--- STDERR ---\n"
			}
			output += result.Stderr
		}

		return &ToolResult{
			Success: result.Success,
			Data:    result,
			Output:  output,
			Error:   result.Error,
			Metadata: map[string]interface{}{
				"command":     result.Command,
				"working_dir": result.WorkingDir,
				"exit_code":   result.ExitCode,
				"duration":    result.Duration.String(),
			},
		}, nil
	}
}

// CreateFileOperationsHandler creates a handler for file operations
func CreateFileOperationsHandler() ToolHandler {
	return func(ctx context.Context, params map[string]interface{}) (*ToolResult, error) {
		// Extract parameters
		operation, ok := params["operation"].(string)
		if !ok || operation == "" {
			return &ToolResult{
				Success: false,
				Error:   "operation parameter is required",
			}, nil
		}

		path, ok := params["path"].(string)
		if !ok || path == "" {
			return &ToolResult{
				Success: false,
				Error:   "path parameter is required",
			}, nil
		}

		// Validate and sanitize path
		if !utils.IsValidPath(path) {
			return &ToolResult{
				Success: false,
				Error:   "invalid or unsafe path",
			}, nil
		}

		path = utils.SanitizePath(path)

		switch operation {
		case "read":
			return handleFileRead(path)
		case "write":
			content := utils.SafeString(params["content"])
			return handleFileWrite(path, content)
		case "append":
			content := utils.SafeString(params["content"])
			return handleFileAppend(path, content)
		case "delete":
			return handleFileDelete(path)
		case "copy":
			destination := utils.SafeString(params["destination"])
			if destination == "" {
				return &ToolResult{
					Success: false,
					Error:   "destination parameter is required for copy operation",
				}, nil
			}
			return handleFileCopy(path, destination)
		case "move":
			destination := utils.SafeString(params["destination"])
			if destination == "" {
				return &ToolResult{
					Success: false,
					Error:   "destination parameter is required for move operation",
				}, nil
			}
			return handleFileMove(path, destination)
		case "exists":
			return handleFileExists(path)
		case "stat":
			return handleFileStat(path)
		default:
			return &ToolResult{
				Success: false,
				Error:   fmt.Sprintf("unsupported operation: %s", operation),
			}, nil
		}
	}
}

// CreateSystemInfoHandler creates a handler for system information
func CreateSystemInfoHandler() ToolHandler {
	return func(ctx context.Context, params map[string]interface{}) (*ToolResult, error) {
		infoType, ok := params["info_type"].(string)
		if !ok || infoType == "" {
			return &ToolResult{
				Success: false,
				Error:   "info_type parameter is required",
			}, nil
		}

		detailed := utils.SafeBool(params["detailed"])

		switch infoType {
		case "cpu":
			return handleCPUInfo(detailed)
		case "memory":
			return handleMemoryInfo(detailed)
		case "disk":
			return handleDiskInfo(detailed)
		case "network":
			return handleNetworkInfo(detailed)
		case "processes":
			return handleProcessInfo(detailed)
		case "environment":
			return handleEnvironmentInfo(detailed)
		case "all":
			return handleAllSystemInfo(detailed)
		default:
			return &ToolResult{
				Success: false,
				Error:   fmt.Sprintf("unsupported info_type: %s", infoType),
			}, nil
		}
	}
}

// CreateProcessManagementHandler creates a handler for process management
func CreateProcessManagementHandler() ToolHandler {
	return func(ctx context.Context, params map[string]interface{}) (*ToolResult, error) {
		action, ok := params["action"].(string)
		if !ok || action == "" {
			return &ToolResult{
				Success: false,
				Error:   "action parameter is required",
			}, nil
		}

		switch action {
		case "list":
			processName := utils.SafeString(params["process_name"])
			return handleProcessList(processName)
		case "info":
			processID := utils.SafeInt(params["process_id"])
			if processID == 0 {
				return &ToolResult{
					Success: false,
					Error:   "process_id parameter is required for info action",
				}, nil
			}
			return handleProcessInfoByID(processID)
		case "kill":
			processID := utils.SafeInt(params["process_id"])
			if processID == 0 {
				return &ToolResult{
					Success: false,
					Error:   "process_id parameter is required for kill action",
				}, nil
			}
			return handleProcessKill(processID)
		case "signal":
			processID := utils.SafeInt(params["process_id"])
			signal := utils.SafeString(params["signal"])
			if processID == 0 || signal == "" {
				return &ToolResult{
					Success: false,
					Error:   "process_id and signal parameters are required for signal action",
				}, nil
			}
			return handleProcessSignal(processID, signal)
		default:
			return &ToolResult{
				Success: false,
				Error:   fmt.Sprintf("unsupported action: %s", action),
			}, nil
		}
	}
}

// File operation handlers

func handleFileRead(path string) (*ToolResult, error) {
	content, err := os.ReadFile(path)
	if err != nil {
		return &ToolResult{
			Success: false,
			Error:   fmt.Sprintf("failed to read file: %v", err),
		}, nil
	}

	return &ToolResult{
		Success: true,
		Data:    string(content),
		Output:  fmt.Sprintf("Successfully read %d bytes from %s", len(content), path),
		Metadata: map[string]interface{}{
			"path": path,
			"size": len(content),
		},
	}, nil
}

func handleFileWrite(path, content string) (*ToolResult, error) {
	err := os.WriteFile(path, []byte(content), 0644)
	if err != nil {
		return &ToolResult{
			Success: false,
			Error:   fmt.Sprintf("failed to write file: %v", err),
		}, nil
	}

	return &ToolResult{
		Success: true,
		Output:  fmt.Sprintf("Successfully wrote %d bytes to %s", len(content), path),
		Metadata: map[string]interface{}{
			"path": path,
			"size": len(content),
		},
	}, nil
}

func handleFileAppend(path, content string) (*ToolResult, error) {
	file, err := os.OpenFile(path, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		return &ToolResult{
			Success: false,
			Error:   fmt.Sprintf("failed to open file for append: %v", err),
		}, nil
	}
	defer file.Close()

	_, err = file.WriteString(content)
	if err != nil {
		return &ToolResult{
			Success: false,
			Error:   fmt.Sprintf("failed to append to file: %v", err),
		}, nil
	}

	return &ToolResult{
		Success: true,
		Output:  fmt.Sprintf("Successfully appended %d bytes to %s", len(content), path),
		Metadata: map[string]interface{}{
			"path": path,
			"size": len(content),
		},
	}, nil
}

func handleFileDelete(path string) (*ToolResult, error) {
	err := os.Remove(path)
	if err != nil {
		return &ToolResult{
			Success: false,
			Error:   fmt.Sprintf("failed to delete file: %v", err),
		}, nil
	}

	return &ToolResult{
		Success: true,
		Output:  fmt.Sprintf("Successfully deleted %s", path),
		Metadata: map[string]interface{}{
			"path": path,
		},
	}, nil
}

func handleFileCopy(src, dst string) (*ToolResult, error) {
	// Read source file
	content, err := os.ReadFile(src)
	if err != nil {
		return &ToolResult{
			Success: false,
			Error:   fmt.Sprintf("failed to read source file: %v", err),
		}, nil
	}

	// Write to destination
	err = os.WriteFile(dst, content, 0644)
	if err != nil {
		return &ToolResult{
			Success: false,
			Error:   fmt.Sprintf("failed to write destination file: %v", err),
		}, nil
	}

	return &ToolResult{
		Success: true,
		Output:  fmt.Sprintf("Successfully copied %s to %s (%d bytes)", src, dst, len(content)),
		Metadata: map[string]interface{}{
			"source":      src,
			"destination": dst,
			"size":        len(content),
		},
	}, nil
}

func handleFileMove(src, dst string) (*ToolResult, error) {
	err := os.Rename(src, dst)
	if err != nil {
		return &ToolResult{
			Success: false,
			Error:   fmt.Sprintf("failed to move file: %v", err),
		}, nil
	}

	return &ToolResult{
		Success: true,
		Output:  fmt.Sprintf("Successfully moved %s to %s", src, dst),
		Metadata: map[string]interface{}{
			"source":      src,
			"destination": dst,
		},
	}, nil
}

func handleFileExists(path string) (*ToolResult, error) {
	exists := utils.FileExists(path)
	
	return &ToolResult{
		Success: true,
		Data:    exists,
		Output:  fmt.Sprintf("File %s exists: %t", path, exists),
		Metadata: map[string]interface{}{
			"path":   path,
			"exists": exists,
		},
	}, nil
}

func handleFileStat(path string) (*ToolResult, error) {
	info, err := os.Stat(path)
	if err != nil {
		return &ToolResult{
			Success: false,
			Error:   fmt.Sprintf("failed to get file info: %v", err),
		}, nil
	}

	data := map[string]interface{}{
		"name":    info.Name(),
		"size":    info.Size(),
		"mode":    info.Mode().String(),
		"modtime": info.ModTime(),
		"is_dir":  info.IsDir(),
	}

	output := fmt.Sprintf("File: %s\nSize: %s\nMode: %s\nModified: %s\nIs Directory: %t",
		info.Name(),
		utils.FormatBytes(info.Size()),
		info.Mode().String(),
		info.ModTime().Format(time.RFC3339),
		info.IsDir())

	return &ToolResult{
		Success: true,
		Data:    data,
		Output:  output,
		Metadata: map[string]interface{}{
			"path": path,
		},
	}, nil
}

// System info handlers (simplified implementations)

func handleCPUInfo(detailed bool) (*ToolResult, error) {
	info := map[string]interface{}{
		"num_cpu":    runtime.NumCPU(),
		"go_version": runtime.Version(),
		"os":         runtime.GOOS,
		"arch":       runtime.GOARCH,
	}

	output := fmt.Sprintf("CPU Count: %d\nArchitecture: %s\nOS: %s",
		runtime.NumCPU(), runtime.GOARCH, runtime.GOOS)

	return &ToolResult{
		Success: true,
		Data:    info,
		Output:  output,
	}, nil
}

func handleMemoryInfo(detailed bool) (*ToolResult, error) {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	info := map[string]interface{}{
		"alloc":      m.Alloc,
		"total_alloc": m.TotalAlloc,
		"sys":        m.Sys,
		"num_gc":     m.NumGC,
	}

	output := fmt.Sprintf("Memory Allocated: %s\nTotal Allocated: %s\nSystem Memory: %s\nGC Runs: %d",
		utils.FormatBytes(int64(m.Alloc)),
		utils.FormatBytes(int64(m.TotalAlloc)),
		utils.FormatBytes(int64(m.Sys)),
		m.NumGC)

	return &ToolResult{
		Success: true,
		Data:    info,
		Output:  output,
	}, nil
}

func handleDiskInfo(detailed bool) (*ToolResult, error) {
	// This is a simplified implementation
	// In a real implementation, you would use system calls to get disk info
	info := map[string]interface{}{
		"message": "Disk information requires system-specific implementation",
	}

	return &ToolResult{
		Success: true,
		Data:    info,
		Output:  "Disk information not available in this simplified implementation",
	}, nil
}

func handleNetworkInfo(detailed bool) (*ToolResult, error) {
	hostname, _ := os.Hostname()
	
	info := map[string]interface{}{
		"hostname": hostname,
	}

	return &ToolResult{
		Success: true,
		Data:    info,
		Output:  fmt.Sprintf("Hostname: %s", hostname),
	}, nil
}

func handleProcessInfo(detailed bool) (*ToolResult, error) {
	info := map[string]interface{}{
		"num_goroutine": runtime.NumGoroutine(),
		"pid":           os.Getpid(),
	}

	output := fmt.Sprintf("Process ID: %d\nGoroutines: %d",
		os.Getpid(), runtime.NumGoroutine())

	return &ToolResult{
		Success: true,
		Data:    info,
		Output:  output,
	}, nil
}

func handleEnvironmentInfo(detailed bool) (*ToolResult, error) {
	env := os.Environ()
	info := make(map[string]interface{})
	
	for _, e := range env {
		parts := strings.SplitN(e, "=", 2)
		if len(parts) == 2 {
			info[parts[0]] = parts[1]
		}
	}

	output := fmt.Sprintf("Environment variables: %d entries", len(env))

	return &ToolResult{
		Success: true,
		Data:    info,
		Output:  output,
	}, nil
}

func handleAllSystemInfo(detailed bool) (*ToolResult, error) {
	systemInfo := utils.GetSystemInfo()
	
	var output strings.Builder
	output.WriteString("System Information:\n")
	for key, value := range systemInfo {
		output.WriteString(fmt.Sprintf("%s: %s\n", key, value))
	}

	return &ToolResult{
		Success: true,
		Data:    systemInfo,
		Output:  output.String(),
	}, nil
}

// Process management handlers (simplified implementations)

func handleProcessList(processName string) (*ToolResult, error) {
	// This is a simplified implementation
	// In a real implementation, you would use system calls to list processes
	info := map[string]interface{}{
		"message": "Process listing requires system-specific implementation",
		"filter":  processName,
	}

	output := "Process listing not available in this simplified implementation"
	if processName != "" {
		output += fmt.Sprintf(" (filter: %s)", processName)
	}

	return &ToolResult{
		Success: true,
		Data:    info,
		Output:  output,
	}, nil
}

func handleProcessInfoByID(processID int) (*ToolResult, error) {
	info := map[string]interface{}{
		"pid":     processID,
		"message": "Process info requires system-specific implementation",
	}

	return &ToolResult{
		Success: true,
		Data:    info,
		Output:  fmt.Sprintf("Process info for PID %d not available in this simplified implementation", processID),
	}, nil
}

func handleProcessKill(processID int) (*ToolResult, error) {
	// This would require careful implementation with proper safety checks
	return &ToolResult{
		Success: false,
		Error:   "Process killing requires explicit user confirmation and system-specific implementation",
	}, nil
}

func handleProcessSignal(processID int, signal string) (*ToolResult, error) {
	// This would require careful implementation with proper safety checks
	return &ToolResult{
		Success: false,
		Error:   "Process signaling requires explicit user confirmation and system-specific implementation",
	}, nil
}
