package components

import (
	"fmt"
	"time"

	"github.com/charmbracelet/bubbles/spinner"
	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
)

// BallSpinner represents a custom ball spinner with elapsed time
type BallSpinner struct {
	spinner   spinner.Model
	startTime time.Time
	style     lipgloss.Style
}

// NewBallSpinner creates a new ball spinner
func NewBallSpinner() BallSpinner {
	s := spinner.New()
	
	// Custom ball animation frames
	ballFrames := []string{
		"( ●    )",
		"(  ●   )",
		"(   ●  )",
		"(    ● )",
		"(     ●)",
		"(    ● )",
		"(   ●  )",
		"(  ●   )",
		"( ●    )",
		"(●     )",
	}
	
	s.Spinner = spinner.Spinner{
		Frames: ballFrames,
		FPS:    time.Second / 8, // 8 FPS for smooth animation
	}
	
	style := lipgloss.NewStyle().
		Foreground(lipgloss.Color("#00ff00")).
		Bold(true)
	
	return BallSpinner{
		spinner:   s,
		startTime: time.Now(),
		style:     style,
	}
}

// Init initializes the spinner
func (bs BallSpinner) Init() tea.Cmd {
	return bs.spinner.Tick
}

// Update updates the spinner state
func (bs BallSpinner) Update(msg tea.Msg) (BallSpinner, tea.Cmd) {
	var cmd tea.Cmd
	bs.spinner, cmd = bs.spinner.Update(msg)
	return bs, cmd
}

// View renders the spinner with elapsed time
func (bs BallSpinner) View() string {
	elapsed := time.Since(bs.startTime)
	elapsedStr := formatDuration(elapsed)
	
	return bs.style.Render(fmt.Sprintf("%s %s", bs.spinner.View(), elapsedStr))
}

// ViewWithMessage renders the spinner with a custom message and elapsed time
func (bs BallSpinner) ViewWithMessage(message string) string {
	elapsed := time.Since(bs.startTime)
	elapsedStr := formatDuration(elapsed)
	
	return bs.style.Render(fmt.Sprintf("%s %s - %s", bs.spinner.View(), message, elapsedStr))
}

// Reset resets the spinner start time
func (bs *BallSpinner) Reset() {
	bs.startTime = time.Now()
}

// SetStyle sets the spinner style
func (bs *BallSpinner) SetStyle(style lipgloss.Style) {
	bs.style = style
}



// LoadingSpinner represents different types of loading spinners
type LoadingSpinner struct {
	spinner     spinner.Model
	message     string
	startTime   time.Time
	style       lipgloss.Style
	spinnerType SpinnerType
}

// SpinnerType represents different spinner types
type SpinnerType int

const (
	BallType SpinnerType = iota
	DotsType
	LineType
	PulseType
)

// NewLoadingSpinner creates a new loading spinner
func NewLoadingSpinner(spinnerType SpinnerType, message string) LoadingSpinner {
	s := spinner.New()
	
	switch spinnerType {
	case BallType:
		s.Spinner = spinner.Spinner{
			Frames: []string{
				"( ●    )",
				"(  ●   )",
				"(   ●  )",
				"(    ● )",
				"(     ●)",
				"(    ● )",
				"(   ●  )",
				"(  ●   )",
				"( ●    )",
				"(●     )",
			},
			FPS: time.Second / 8,
		}
	case DotsType:
		s.Spinner = spinner.Dot
	case LineType:
		s.Spinner = spinner.Line
	case PulseType:
		s.Spinner = spinner.Pulse
	default:
		s.Spinner = spinner.Dot
	}
	
	style := lipgloss.NewStyle().
		Foreground(lipgloss.Color("#00ff00")).
		Bold(true)
	
	return LoadingSpinner{
		spinner:     s,
		message:     message,
		startTime:   time.Now(),
		style:       style,
		spinnerType: spinnerType,
	}
}

// Init initializes the loading spinner
func (ls LoadingSpinner) Init() tea.Cmd {
	return ls.spinner.Tick
}

// Update updates the loading spinner state
func (ls LoadingSpinner) Update(msg tea.Msg) (LoadingSpinner, tea.Cmd) {
	var cmd tea.Cmd
	ls.spinner, cmd = ls.spinner.Update(msg)
	return ls, cmd
}

// View renders the loading spinner
func (ls LoadingSpinner) View() string {
	elapsed := time.Since(ls.startTime)
	elapsedStr := formatDuration(elapsed)
	
	if ls.message != "" {
		return ls.style.Render(fmt.Sprintf("%s %s (%s)", ls.spinner.View(), ls.message, elapsedStr))
	}
	
	return ls.style.Render(fmt.Sprintf("%s (%s)", ls.spinner.View(), elapsedStr))
}

// SetMessage sets the spinner message
func (ls *LoadingSpinner) SetMessage(message string) {
	ls.message = message
}

// Reset resets the spinner start time
func (ls *LoadingSpinner) Reset() {
	ls.startTime = time.Now()
}

// SetStyle sets the spinner style
func (ls *LoadingSpinner) SetStyle(style lipgloss.Style) {
	ls.style = style
}

// ProgressSpinner combines a spinner with progress information
type ProgressSpinner struct {
	spinner     LoadingSpinner
	current     int
	total       int
	showPercent bool
}

// NewProgressSpinner creates a new progress spinner
func NewProgressSpinner(total int, message string) ProgressSpinner {
	return ProgressSpinner{
		spinner:     NewLoadingSpinner(BallType, message),
		current:     0,
		total:       total,
		showPercent: true,
	}
}

// Init initializes the progress spinner
func (ps ProgressSpinner) Init() tea.Cmd {
	return ps.spinner.Init()
}

// Update updates the progress spinner state
func (ps ProgressSpinner) Update(msg tea.Msg) (ProgressSpinner, tea.Cmd) {
	var cmd tea.Cmd
	ps.spinner, cmd = ps.spinner.Update(msg)
	return ps, cmd
}

// View renders the progress spinner
func (ps ProgressSpinner) View() string {
	baseView := ps.spinner.View()
	
	if ps.total > 0 && ps.showPercent {
		percent := float64(ps.current) / float64(ps.total) * 100
		return fmt.Sprintf("%s [%.1f%%]", baseView, percent)
	}
	
	if ps.total > 0 {
		return fmt.Sprintf("%s [%d/%d]", baseView, ps.current, ps.total)
	}
	
	return baseView
}

// SetProgress sets the current progress
func (ps *ProgressSpinner) SetProgress(current int) {
	ps.current = current
}

// Increment increments the progress by 1
func (ps *ProgressSpinner) Increment() {
	ps.current++
}

// SetTotal sets the total progress
func (ps *ProgressSpinner) SetTotal(total int) {
	ps.total = total
}

// SetShowPercent sets whether to show percentage
func (ps *ProgressSpinner) SetShowPercent(show bool) {
	ps.showPercent = show
}

// IsComplete returns true if progress is complete
func (ps ProgressSpinner) IsComplete() bool {
	return ps.current >= ps.total && ps.total > 0
}
