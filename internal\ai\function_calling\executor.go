package function_calling

import (
	"context"
	"fmt"
	"sync"
	"time"

	"arien-ai/internal/errors"
	"arien-ai/internal/retry"
)

// ExecutionContext contains context for tool execution
type ExecutionContext struct {
	UserID      string
	SessionID   string
	RequestID   string
	Permissions []string
	Metadata    map[string]interface{}
}

// ExecutionResult contains the result of tool execution
type ExecutionResult struct {
	ToolCall *ToolCall   `json:"tool_call"`
	Result   *ToolResult `json:"result"`
	Duration time.Duration `json:"duration"`
	Error    error       `json:"error,omitempty"`
}

// ExecutionPlan represents a plan for executing multiple tools
type ExecutionPlan struct {
	Tools       []*ToolCall `json:"tools"`
	Parallel    bool        `json:"parallel"`
	MaxRetries  int         `json:"max_retries"`
	Timeout     time.Duration `json:"timeout"`
	Context     *ExecutionContext `json:"context"`
}

// Executor handles the execution of function calls
type Executor struct {
	registry     *Registry
	retryManager *retry.Manager
	errorHandler *errors.Handler
	maxConcurrency int
	defaultTimeout time.Duration
}

// NewExecutor creates a new function call executor
func NewExecutor(registry *Registry, retryManager *retry.Manager, errorHandler *errors.Handler) *Executor {
	return &Executor{
		registry:       registry,
		retryManager:   retryManager,
		errorHandler:   errorHandler,
		maxConcurrency: 5,
		defaultTimeout: 30 * time.Second,
	}
}

// SetMaxConcurrency sets the maximum number of concurrent tool executions
func (e *Executor) SetMaxConcurrency(max int) {
	e.maxConcurrency = max
}

// SetDefaultTimeout sets the default timeout for tool execution
func (e *Executor) SetDefaultTimeout(timeout time.Duration) {
	e.defaultTimeout = timeout
}

// ExecuteSingle executes a single tool call
func (e *Executor) ExecuteSingle(ctx context.Context, toolCall *ToolCall, execCtx *ExecutionContext) (*ExecutionResult, error) {
	start := time.Now()
	
	// Parse arguments
	args, err := toolCall.ParseArguments()
	if err != nil {
		return &ExecutionResult{
			ToolCall: toolCall,
			Duration: time.Since(start),
			Error:    fmt.Errorf("failed to parse arguments: %w", err),
		}, nil
	}
	
	// Get tool definition
	tool, exists := e.registry.GetTool(toolCall.Name)
	if !exists {
		return &ExecutionResult{
			ToolCall: toolCall,
			Duration: time.Since(start),
			Error:    fmt.Errorf("tool %s not found", toolCall.Name),
		}, nil
	}
	
	// Check permissions
	if err := e.checkPermissions(tool, execCtx); err != nil {
		return &ExecutionResult{
			ToolCall: toolCall,
			Duration: time.Since(start),
			Error:    fmt.Errorf("permission denied: %w", err),
		}, nil
	}
	
	// Create execution context with timeout
	execTimeout := e.defaultTimeout
	if timeout, ok := args["timeout"].(float64); ok && timeout > 0 {
		execTimeout = time.Duration(timeout) * time.Second
	}

	execCtxWithTimeout, cancel := context.WithTimeout(ctx, execTimeout)
	defer cancel()

	// Execute with retry
	var result *ToolResult
	err = e.retryManager.Execute(execCtxWithTimeout, func() error {
		var execErr error
		result, execErr = tool.Handler(execCtxWithTimeout, args)
		return execErr
	})
	
	return &ExecutionResult{
		ToolCall: toolCall,
		Result:   result,
		Duration: time.Since(start),
		Error:    err,
	}, nil
}

// ExecutePlan executes a plan containing multiple tool calls
func (e *Executor) ExecutePlan(ctx context.Context, plan *ExecutionPlan) ([]*ExecutionResult, error) {
	if len(plan.Tools) == 0 {
		return []*ExecutionResult{}, nil
	}
	
	// Apply plan timeout
	if plan.Timeout > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, plan.Timeout)
		defer cancel()
	}
	
	if plan.Parallel {
		return e.executeParallel(ctx, plan)
	}
	
	return e.executeSequential(ctx, plan)
}

// executeSequential executes tools sequentially
func (e *Executor) executeSequential(ctx context.Context, plan *ExecutionPlan) ([]*ExecutionResult, error) {
	results := make([]*ExecutionResult, len(plan.Tools))
	
	for i, toolCall := range plan.Tools {
		result, err := e.ExecuteSingle(ctx, toolCall, plan.Context)
		if err != nil {
			return results[:i], fmt.Errorf("failed to execute tool %s: %w", toolCall.Name, err)
		}
		
		results[i] = result
		
		// Check if execution failed and should stop
		if result.Error != nil && !e.shouldContinueOnError(result.Error) {
			return results[:i+1], fmt.Errorf("tool execution failed: %w", result.Error)
		}
		
		// Check context cancellation
		if ctx.Err() != nil {
			return results[:i+1], ctx.Err()
		}
	}
	
	return results, nil
}

// executeParallel executes tools in parallel with concurrency control
func (e *Executor) executeParallel(ctx context.Context, plan *ExecutionPlan) ([]*ExecutionResult, error) {
	results := make([]*ExecutionResult, len(plan.Tools))
	semaphore := make(chan struct{}, e.maxConcurrency)
	var wg sync.WaitGroup
	var mu sync.Mutex
	var firstError error
	
	for i, toolCall := range plan.Tools {
		wg.Add(1)
		go func(index int, tc *ToolCall) {
			defer wg.Done()
			
			// Acquire semaphore
			select {
			case semaphore <- struct{}{}:
				defer func() { <-semaphore }()
			case <-ctx.Done():
				mu.Lock()
				if firstError == nil {
					firstError = ctx.Err()
				}
				mu.Unlock()
				return
			}
			
			// Execute tool
			result, err := e.ExecuteSingle(ctx, tc, plan.Context)
			
			mu.Lock()
			results[index] = result
			if err != nil && firstError == nil {
				firstError = err
			}
			mu.Unlock()
		}(i, toolCall)
	}
	
	wg.Wait()
	
	return results, firstError
}

// checkPermissions checks if the execution context has required permissions
func (e *Executor) checkPermissions(tool *ToolDefinition, execCtx *ExecutionContext) error {
	if !tool.Usage.RequiresAuth {
		return nil
	}
	
	if execCtx == nil || len(execCtx.Permissions) == 0 {
		return fmt.Errorf("tool %s requires authentication", tool.Name)
	}
	
	// Check for required permissions based on safety level
	requiredPerms := e.getRequiredPermissions(tool)
	for _, required := range requiredPerms {
		found := false
		for _, perm := range execCtx.Permissions {
			if perm == required {
				found = true
				break
			}
		}
		if !found {
			return fmt.Errorf("missing required permission: %s", required)
		}
	}
	
	return nil
}

// getRequiredPermissions returns required permissions based on tool safety level
func (e *Executor) getRequiredPermissions(tool *ToolDefinition) []string {
	switch tool.Usage.SafetyLevel {
	case "critical":
		return []string{"admin", "critical_operations"}
	case "high":
		return []string{"elevated", "system_operations"}
	case "medium":
		return []string{"user", "basic_operations"}
	default:
		return []string{}
	}
}

// shouldContinueOnError determines if execution should continue after an error
func (e *Executor) shouldContinueOnError(err error) bool {
	// Check if it's a retryable error
	if retryableErr, ok := err.(interface{ IsRetryable() bool }); ok {
		return retryableErr.IsRetryable()
	}
	
	// Check for specific error types
	switch err {
	case context.Canceled, context.DeadlineExceeded:
		return false
	default:
		return true
	}
}

// ValidatePlan validates an execution plan
func (e *Executor) ValidatePlan(plan *ExecutionPlan) error {
	if plan == nil {
		return fmt.Errorf("execution plan cannot be nil")
	}
	
	if len(plan.Tools) == 0 {
		return fmt.Errorf("execution plan must contain at least one tool")
	}
	
	// Validate each tool call
	for i, toolCall := range plan.Tools {
		if toolCall.Name == "" {
			return fmt.Errorf("tool call %d: name cannot be empty", i)
		}
		
		// Check if tool exists
		if !e.registry.HasTool(toolCall.Name) {
			return fmt.Errorf("tool call %d: tool %s not found", i, toolCall.Name)
		}
		
		// Validate arguments
		if _, err := toolCall.ParseArguments(); err != nil {
			return fmt.Errorf("tool call %d: invalid arguments: %w", i, err)
		}
	}
	
	// Check for conflicting parallel execution requirements
	if plan.Parallel {
		for i, toolCall := range plan.Tools {
			tool, _ := e.registry.GetTool(toolCall.Name)
			if tool.Usage.Sequential {
				return fmt.Errorf("tool call %d: tool %s requires sequential execution", i, toolCall.Name)
			}
		}
	}
	
	return nil
}

// CreatePlanFromToolCalls creates an execution plan from tool calls
func (e *Executor) CreatePlanFromToolCalls(toolCalls []*ToolCall, parallel bool, execCtx *ExecutionContext) (*ExecutionPlan, error) {
	plan := &ExecutionPlan{
		Tools:      toolCalls,
		Parallel:   parallel,
		MaxRetries: 3,
		Timeout:    5 * time.Minute,
		Context:    execCtx,
	}
	
	// Validate the plan
	if err := e.ValidatePlan(plan); err != nil {
		return nil, err
	}
	
	return plan, nil
}

// GetExecutionSummary returns a summary of execution results
func (e *Executor) GetExecutionSummary(results []*ExecutionResult) map[string]interface{} {
	summary := map[string]interface{}{
		"total_tools":     len(results),
		"successful":      0,
		"failed":          0,
		"total_duration":  time.Duration(0),
		"average_duration": time.Duration(0),
		"tools":           []map[string]interface{}{},
	}
	
	var totalDuration time.Duration
	successful := 0
	
	for _, result := range results {
		totalDuration += result.Duration
		
		toolSummary := map[string]interface{}{
			"name":     result.ToolCall.Name,
			"duration": result.Duration.String(),
			"success":  result.Error == nil && (result.Result == nil || result.Result.Success),
		}
		
		if result.Error != nil {
			toolSummary["error"] = result.Error.Error()
		} else if result.Result != nil && !result.Result.Success {
			toolSummary["error"] = result.Result.Error
			successful++
		} else {
			successful++
		}
		
		summary["tools"] = append(summary["tools"].([]map[string]interface{}), toolSummary)
	}
	
	summary["successful"] = successful
	summary["failed"] = len(results) - successful
	summary["total_duration"] = totalDuration
	
	if len(results) > 0 {
		summary["average_duration"] = totalDuration / time.Duration(len(results))
	}
	
	return summary
}
