package providers

import (
	"bufio"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/go-resty/resty/v2"
)

// OllamaChatRequest represents an Ollama chat request
type OllamaChatRequest struct {
	Model    string           `json:"model"`
	Messages []OllamaMessage  `json:"messages"`
	Tools    []OllamaTool     `json:"tools,omitempty"`
	Stream   bool             `json:"stream,omitempty"`
	Options  *OllamaOptions   `json:"options,omitempty"`
}

// OllamaMessage represents an Ollama message
type OllamaMessage struct {
	Role      string           `json:"role"`
	Content   string           `json:"content"`
	ToolCalls []OllamaToolCall `json:"tool_calls,omitempty"`
}

// OllamaTool represents an Ollama tool
type OllamaTool struct {
	Type     string                 `json:"type"`
	Function OllamaToolFunction     `json:"function"`
}

// OllamaToolFunction represents an Ollama tool function
type OllamaToolFunction struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Parameters  map[string]interface{} `json:"parameters"`
}

// OllamaToolCall represents an Ollama tool call
type OllamaToolCall struct {
	ID       string `json:"id"`
	Type     string `json:"type"`
	Function struct {
		Name      string `json:"name"`
		Arguments string `json:"arguments"`
	} `json:"function"`
}

// OllamaOptions represents Ollama generation options
type OllamaOptions struct {
	Temperature float64 `json:"temperature,omitempty"`
	TopP        float64 `json:"top_p,omitempty"`
	TopK        int     `json:"top_k,omitempty"`
	NumPredict  int     `json:"num_predict,omitempty"`
}

// OllamaChatResponse represents an Ollama chat response
type OllamaChatResponse struct {
	Model     string        `json:"model"`
	CreatedAt time.Time     `json:"created_at"`
	Message   OllamaMessage `json:"message"`
	Done      bool          `json:"done"`
	TotalDuration    int64 `json:"total_duration,omitempty"`
	LoadDuration     int64 `json:"load_duration,omitempty"`
	PromptEvalCount  int   `json:"prompt_eval_count,omitempty"`
	PromptEvalDuration int64 `json:"prompt_eval_duration,omitempty"`
	EvalCount        int   `json:"eval_count,omitempty"`
	EvalDuration     int64 `json:"eval_duration,omitempty"`
}

// OllamaProvider implements the AIProvider interface for Ollama API
type OllamaProvider struct {
	config ProviderConfig
	client *resty.Client
	models []string
}

// NewOllamaProvider creates a new Ollama provider
func NewOllamaProvider(config ProviderConfig) *OllamaProvider {
	client := resty.New()
	client.SetBaseURL(config.BaseURL)
	client.SetTimeout(config.Timeout)
	client.SetHeader("Content-Type", "application/json")
	
	// Add custom headers
	for key, value := range config.Headers {
		client.SetHeader(key, value)
	}
	
	return &OllamaProvider{
		config: config,
		client: client,
		models: []string{"llama3.3", "codellama", "mistral", "phi3"},
	}
}

// GetName returns the provider name
func (o *OllamaProvider) GetName() string {
	return "ollama"
}

// GetModels returns the list of available models
func (o *OllamaProvider) GetModels() []string {
	// Try to fetch models from Ollama API
	ctx, cancel := context.WithTimeout(context.Background(), o.config.Timeout)
	defer cancel()
	
	resp, err := o.client.R().
		SetContext(ctx).
		Get("/api/tags")
	
	if err != nil {
		// Return default models if API call fails
		return o.models
	}
	
	if resp.StatusCode() != http.StatusOK {
		return o.models
	}
	
	var tagsResp struct {
		Models []struct {
			Name string `json:"name"`
		} `json:"models"`
	}
	
	if err := json.Unmarshal(resp.Body(), &tagsResp); err != nil {
		return o.models
	}
	
	models := make([]string, len(tagsResp.Models))
	for i, model := range tagsResp.Models {
		models[i] = model.Name
	}
	
	if len(models) > 0 {
		o.models = models
	}
	
	return o.models
}

// SupportsFunctionCalling returns true as Ollama supports function calling with compatible models
func (o *OllamaProvider) SupportsFunctionCalling() bool {
	return true
}

// IsHealthy checks if the provider is healthy
func (o *OllamaProvider) IsHealthy(ctx context.Context) error {
	resp, err := o.client.R().
		SetContext(ctx).
		Get("/api/tags")
	
	if err != nil {
		return NewProviderError("ollama", "connection_error", "Failed to connect to Ollama API", true, err)
	}
	
	if resp.StatusCode() != http.StatusOK {
		return NewProviderError("ollama", "api_error", fmt.Sprintf("API returned status %d", resp.StatusCode()), false, nil)
	}
	
	return nil
}

// Chat sends a chat request and returns the response
func (o *OllamaProvider) Chat(ctx context.Context, request *ChatRequest) (*ChatResponse, error) {
	// Convert to Ollama format
	ollamaReq := o.convertToOllamaRequest(request)
	
	// Prepare the request
	reqBody, err := json.Marshal(ollamaReq)
	if err != nil {
		return nil, NewProviderError("ollama", "marshal_error", "Failed to marshal request", false, err)
	}
	
	// Send the request
	resp, err := o.client.R().
		SetContext(ctx).
		SetBody(reqBody).
		Post("/api/chat")
	
	if err != nil {
		return nil, o.handleError(err)
	}
	
	// Check status code
	if resp.StatusCode() != http.StatusOK {
		return nil, o.handleHTTPError(resp)
	}
	
	// Parse response
	var ollamaResp OllamaChatResponse
	if err := json.Unmarshal(resp.Body(), &ollamaResp); err != nil {
		return nil, NewProviderError("ollama", "unmarshal_error", "Failed to unmarshal response", false, err)
	}
	
	// Convert to standard format
	chatResp := o.convertFromOllamaResponse(&ollamaResp)
	return chatResp, nil
}

// StreamChat sends a chat request and returns a stream of responses
func (o *OllamaProvider) StreamChat(ctx context.Context, request *ChatRequest) (<-chan StreamResponse, error) {
	// Convert to Ollama format
	ollamaReq := o.convertToOllamaRequest(request)
	ollamaReq.Stream = true
	
	// Prepare the request
	reqBody, err := json.Marshal(ollamaReq)
	if err != nil {
		return nil, NewProviderError("ollama", "marshal_error", "Failed to marshal request", false, err)
	}
	
	// Create HTTP request
	httpReq, err := http.NewRequestWithContext(ctx, "POST", o.config.BaseURL+"/api/chat", bytes.NewReader(reqBody))
	if err != nil {
		return nil, NewProviderError("ollama", "request_error", "Failed to create request", false, err)
	}
	
	// Set headers
	httpReq.Header.Set("Content-Type", "application/json")
	
	// Add custom headers
	for key, value := range o.config.Headers {
		httpReq.Header.Set(key, value)
	}
	
	// Send request
	client := &http.Client{Timeout: o.config.Timeout}
	resp, err := client.Do(httpReq)
	if err != nil {
		return nil, o.handleError(err)
	}
	
	if resp.StatusCode != http.StatusOK {
		resp.Body.Close()
		return nil, NewProviderError("ollama", "http_error", fmt.Sprintf("HTTP %d", resp.StatusCode), false, nil)
	}
	
	// Create response channel
	responseChan := make(chan StreamResponse, 10)
	
	// Start goroutine to read stream
	go func() {
		defer close(responseChan)
		defer resp.Body.Close()
		
		scanner := bufio.NewScanner(resp.Body)
		for scanner.Scan() {
			line := scanner.Text()
			
			// Skip empty lines
			if line == "" {
				continue
			}
			
			// Parse JSON
			var ollamaResp OllamaChatResponse
			if err := json.Unmarshal([]byte(line), &ollamaResp); err != nil {
				streamResp := StreamResponse{
					Error: NewProviderError("ollama", "parse_error", "Failed to parse stream data", false, err),
				}
				select {
				case responseChan <- streamResp:
				case <-ctx.Done():
					return
				}
				continue
			}
			
			// Convert to standard format
			streamResp := StreamResponse{
				ID:      ollamaResp.Model,
				Object:  "chat.completion.chunk",
				Created: ollamaResp.CreatedAt.Unix(),
				Model:   ollamaResp.Model,
				Choices: []StreamChoice{
					{
						Index: 0,
						Delta: Delta{
							Content: ollamaResp.Message.Content,
							Role:    ollamaResp.Message.Role,
						},
						FinishReason: "",
					},
				},
			}
			
			// Check if done
			if ollamaResp.Done {
				streamResp.Choices[0].FinishReason = "stop"
			}
			
			// Send response
			select {
			case responseChan <- streamResp:
			case <-ctx.Done():
				return
			}
			
			// Break if done
			if ollamaResp.Done {
				return
			}
		}
		
		if err := scanner.Err(); err != nil {
			streamResp := StreamResponse{
				Error: NewProviderError("ollama", "stream_error", "Stream reading error", true, err),
			}
			select {
			case responseChan <- streamResp:
			case <-ctx.Done():
			}
		}
	}()
	
	return responseChan, nil
}

// handleError handles various types of errors
func (o *OllamaProvider) handleError(err error) *ProviderError {
	if err == nil {
		return nil
	}
	
	// Check for timeout
	if err == context.DeadlineExceeded {
		return NewProviderError("ollama", "timeout", "Request timeout", true, err)
	}
	
	// Check for context cancellation
	if err == context.Canceled {
		return NewProviderError("ollama", "canceled", "Request canceled", false, err)
	}
	
	// Default to connection error
	return NewProviderError("ollama", "connection_error", "Connection error", true, err)
}

// handleHTTPError handles HTTP errors
func (o *OllamaProvider) handleHTTPError(resp *resty.Response) *ProviderError {
	statusCode := resp.StatusCode()
	
	// Try to parse error response
	var errorResp struct {
		Error string `json:"error"`
	}
	
	if err := json.Unmarshal(resp.Body(), &errorResp); err == nil && errorResp.Error != "" {
		retryable := statusCode >= 500 // Server errors are retryable
		return NewProviderError("ollama", "api_error", errorResp.Error, retryable, nil)
	}
	
	// Fallback to status code
	message := fmt.Sprintf("HTTP %d: %s", statusCode, http.StatusText(statusCode))
	retryable := statusCode >= 500
	
	return NewProviderError("ollama", "http_error", message, retryable, nil)
}

// convertToOllamaRequest converts a standard ChatRequest to Ollama format
func (o *OllamaProvider) convertToOllamaRequest(request *ChatRequest) *OllamaChatRequest {
	ollamaReq := &OllamaChatRequest{
		Model:    request.Model,
		Messages: make([]OllamaMessage, len(request.Messages)),
		Stream:   request.Stream,
	}

	// Convert messages
	for i, msg := range request.Messages {
		ollamaReq.Messages[i] = OllamaMessage{
			Role:    msg.Role,
			Content: msg.Content,
		}
	}

	// Convert tools
	if len(request.Tools) > 0 {
		ollamaReq.Tools = make([]OllamaTool, len(request.Tools))
		for i, tool := range request.Tools {
			ollamaReq.Tools[i] = OllamaTool{
				Type: tool.Type,
				Function: OllamaToolFunction{
					Name:        tool.Function.Name,
					Description: tool.Function.Description,
					Parameters:  tool.Function.Parameters,
				},
			}
		}
	}

	// Set options
	if request.Temperature > 0 || request.MaxTokens > 0 {
		ollamaReq.Options = &OllamaOptions{
			Temperature: request.Temperature,
		}
		if request.MaxTokens > 0 {
			ollamaReq.Options.NumPredict = request.MaxTokens
		}
	}

	return ollamaReq
}

// convertFromOllamaResponse converts an Ollama response to standard format
func (o *OllamaProvider) convertFromOllamaResponse(ollamaResp *OllamaChatResponse) *ChatResponse {
	resp := &ChatResponse{
		ID:      ollamaResp.Model + "-" + fmt.Sprintf("%d", ollamaResp.CreatedAt.Unix()),
		Object:  "chat.completion",
		Created: ollamaResp.CreatedAt.Unix(),
		Model:   ollamaResp.Model,
		Choices: []Choice{
			{
				Index: 0,
				Message: Message{
					Role:    ollamaResp.Message.Role,
					Content: ollamaResp.Message.Content,
				},
				FinishReason: "stop",
			},
		},
	}

	// Convert tool calls if present
	if len(ollamaResp.Message.ToolCalls) > 0 {
		resp.Choices[0].ToolCalls = make([]ToolCall, len(ollamaResp.Message.ToolCalls))
		for i, tc := range ollamaResp.Message.ToolCalls {
			resp.Choices[0].ToolCalls[i] = ToolCall{
				ID:   tc.ID,
				Type: tc.Type,
				Function: struct {
					Name      string `json:"name"`
					Arguments string `json:"arguments"`
				}{
					Name:      tc.Function.Name,
					Arguments: tc.Function.Arguments,
				},
			}
		}
	}

	// Set usage information if available
	if ollamaResp.PromptEvalCount > 0 || ollamaResp.EvalCount > 0 {
		resp.Usage = Usage{
			PromptTokens:     ollamaResp.PromptEvalCount,
			CompletionTokens: ollamaResp.EvalCount,
			TotalTokens:      ollamaResp.PromptEvalCount + ollamaResp.EvalCount,
		}
	}

	return resp
}
