package config

import (
	"fmt"
	"os"
	"path/filepath"
	"time"

	"github.com/spf13/viper"
)

// Config represents the application configuration
type Config struct {
	AI       AIConfig       `mapstructure:"ai" yaml:"ai"`
	UI       UIConfig       `mapstructure:"ui" yaml:"ui"`
	Security SecurityConfig `mapstructure:"security" yaml:"security"`
	Retry    RetryConfig    `mapstructure:"retry" yaml:"retry"`
	Logging  LoggingConfig  `mapstructure:"logging" yaml:"logging"`
}

// AIConfig contains AI provider configurations
type AIConfig struct {
	Providers ProvidersConfig `mapstructure:"providers" yaml:"providers"`
}

// ProvidersConfig contains configuration for AI providers
type ProvidersConfig struct {
	Deepseek DeepseekConfig `mapstructure:"deepseek" yaml:"deepseek"`
	Ollama   OllamaConfig   `mapstructure:"ollama" yaml:"ollama"`
}

// DeepseekConfig contains Deepseek API configuration
type DeepseekConfig struct {
	APIKey     string            `mapstructure:"api_key" yaml:"api_key"`
	BaseURL    string            `mapstructure:"base_url" yaml:"base_url"`
	Models     DeepseekModels    `mapstructure:"models" yaml:"models"`
	Timeout    time.Duration     `mapstructure:"timeout" yaml:"timeout"`
	MaxRetries int               `mapstructure:"max_retries" yaml:"max_retries"`
	Headers    map[string]string `mapstructure:"headers" yaml:"headers"`
}

// DeepseekModels contains Deepseek model names
type DeepseekModels struct {
	Chat      string `mapstructure:"chat" yaml:"chat"`
	Reasoning string `mapstructure:"reasoning" yaml:"reasoning"`
}

// OllamaConfig contains Ollama configuration
type OllamaConfig struct {
	BaseURL    string            `mapstructure:"base_url" yaml:"base_url"`
	Models     OllamaModels      `mapstructure:"models" yaml:"models"`
	Timeout    time.Duration     `mapstructure:"timeout" yaml:"timeout"`
	MaxRetries int               `mapstructure:"max_retries" yaml:"max_retries"`
	Headers    map[string]string `mapstructure:"headers" yaml:"headers"`
}

// OllamaModels contains Ollama model names
type OllamaModels struct {
	Default string `mapstructure:"default" yaml:"default"`
	Coding  string `mapstructure:"coding" yaml:"coding"`
}

// UIConfig contains UI configuration
type UIConfig struct {
	Theme        string    `mapstructure:"theme" yaml:"theme"`
	Animations   bool      `mapstructure:"animations" yaml:"animations"`
	SpinnerStyle string    `mapstructure:"spinner_style" yaml:"spinner_style"`
	Colors       UIColors  `mapstructure:"colors" yaml:"colors"`
}

// UIColors contains color configuration
type UIColors struct {
	Primary   string `mapstructure:"primary" yaml:"primary"`
	Secondary string `mapstructure:"secondary" yaml:"secondary"`
	Error     string `mapstructure:"error" yaml:"error"`
	Warning   string `mapstructure:"warning" yaml:"warning"`
	Success   string `mapstructure:"success" yaml:"success"`
}

// SecurityConfig contains security settings
type SecurityConfig struct {
	DangerousCommands   []string      `mapstructure:"dangerous_commands" yaml:"dangerous_commands"`
	RequireConfirmation bool          `mapstructure:"require_confirmation" yaml:"require_confirmation"`
	MaxExecutionTime    time.Duration `mapstructure:"max_execution_time" yaml:"max_execution_time"`
	SandboxMode         bool          `mapstructure:"sandbox_mode" yaml:"sandbox_mode"`
	AllowedDirectories  []string      `mapstructure:"allowed_directories" yaml:"allowed_directories"`
}

// RetryConfig contains retry logic configuration
type RetryConfig struct {
	MaxAttempts        int           `mapstructure:"max_attempts" yaml:"max_attempts"`
	InitialDelay       time.Duration `mapstructure:"initial_delay" yaml:"initial_delay"`
	MaxDelay           time.Duration `mapstructure:"max_delay" yaml:"max_delay"`
	BackoffMultiplier  float64       `mapstructure:"backoff_multiplier" yaml:"backoff_multiplier"`
	Jitter             bool          `mapstructure:"jitter" yaml:"jitter"`
}

// LoggingConfig contains logging configuration
type LoggingConfig struct {
	Level      string `mapstructure:"level" yaml:"level"`
	File       string `mapstructure:"file" yaml:"file"`
	MaxSize    string `mapstructure:"max_size" yaml:"max_size"`
	MaxBackups int    `mapstructure:"max_backups" yaml:"max_backups"`
	Console    bool   `mapstructure:"console" yaml:"console"`
}

// Load loads the configuration from file and environment variables
func Load() (*Config, error) {
	config := &Config{}
	
	// Set defaults
	setDefaults()
	
	// Unmarshal configuration
	if err := viper.Unmarshal(config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}
	
	// Validate configuration
	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("invalid configuration: %w", err)
	}
	
	// Ensure config directory exists
	if err := ensureConfigDir(); err != nil {
		return nil, fmt.Errorf("failed to create config directory: %w", err)
	}
	
	return config, nil
}

// setDefaults sets default configuration values
func setDefaults() {
	// AI defaults
	viper.SetDefault("ai.providers.deepseek.base_url", "https://api.deepseek.com/v1")
	viper.SetDefault("ai.providers.deepseek.models.chat", "deepseek-chat")
	viper.SetDefault("ai.providers.deepseek.models.reasoning", "deepseek-reasoner")
	viper.SetDefault("ai.providers.deepseek.timeout", "30s")
	viper.SetDefault("ai.providers.deepseek.max_retries", 3)
	
	viper.SetDefault("ai.providers.ollama.base_url", "http://localhost:11434")
	viper.SetDefault("ai.providers.ollama.models.default", "llama3.3")
	viper.SetDefault("ai.providers.ollama.models.coding", "codellama")
	viper.SetDefault("ai.providers.ollama.timeout", "60s")
	
	// UI defaults
	viper.SetDefault("ui.theme", "dark")
	viper.SetDefault("ui.animations", true)
	viper.SetDefault("ui.spinner_style", "ball")
	viper.SetDefault("ui.colors.primary", "#00ff00")
	viper.SetDefault("ui.colors.secondary", "#0080ff")
	viper.SetDefault("ui.colors.error", "#ff0000")
	viper.SetDefault("ui.colors.warning", "#ffaa00")
	viper.SetDefault("ui.colors.success", "#00ff00")
	
	// Security defaults
	viper.SetDefault("security.dangerous_commands", []string{
		"rm -rf", "sudo rm", "format", "del /f /s /q", "mkfs", "fdisk",
		"dd if=", "shutdown", "reboot", "halt", "poweroff",
	})
	viper.SetDefault("security.require_confirmation", true)
	viper.SetDefault("security.max_execution_time", "300s")
	viper.SetDefault("security.sandbox_mode", false)
	
	// Retry defaults
	viper.SetDefault("retry.max_attempts", 3)
	viper.SetDefault("retry.initial_delay", "1s")
	viper.SetDefault("retry.max_delay", "30s")
	viper.SetDefault("retry.backoff_multiplier", 2.0)
	viper.SetDefault("retry.jitter", true)
	
	// Logging defaults
	home, _ := os.UserHomeDir()
	viper.SetDefault("logging.level", "info")
	viper.SetDefault("logging.file", filepath.Join(home, ".arien-ai", "logs", "arien-ai.log"))
	viper.SetDefault("logging.max_size", "100MB")
	viper.SetDefault("logging.max_backups", 5)
	viper.SetDefault("logging.console", true)
}

// Validate validates the configuration
func (c *Config) Validate() error {
	// Validate AI configuration
	if c.AI.Providers.Deepseek.APIKey == "" {
		if apiKey := os.Getenv("DEEPSEEK_API_KEY"); apiKey != "" {
			c.AI.Providers.Deepseek.APIKey = apiKey
		}
	}
	
	// Validate timeout values
	if c.AI.Providers.Deepseek.Timeout <= 0 {
		return fmt.Errorf("deepseek timeout must be positive")
	}
	if c.AI.Providers.Ollama.Timeout <= 0 {
		return fmt.Errorf("ollama timeout must be positive")
	}
	
	// Validate retry configuration
	if c.Retry.MaxAttempts <= 0 {
		return fmt.Errorf("retry max_attempts must be positive")
	}
	if c.Retry.BackoffMultiplier <= 1.0 {
		return fmt.Errorf("retry backoff_multiplier must be greater than 1.0")
	}
	
	return nil
}

// ensureConfigDir ensures the configuration directory exists
func ensureConfigDir() error {
	home, err := os.UserHomeDir()
	if err != nil {
		return err
	}
	
	configDir := filepath.Join(home, ".arien-ai")
	logsDir := filepath.Join(configDir, "logs")
	
	if err := os.MkdirAll(configDir, 0755); err != nil {
		return err
	}
	
	if err := os.MkdirAll(logsDir, 0755); err != nil {
		return err
	}
	
	return nil
}
