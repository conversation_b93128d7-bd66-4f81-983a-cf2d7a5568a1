package cmd

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/spf13/cobra"
	"arien-ai/internal/ai/providers"
	"arien-ai/internal/ai/function_calling"
	"arien-ai/internal/ai/prompts"
	"arien-ai/internal/shell"
	"arien-ai/internal/ui/components"
)

// executeCmd represents the execute command
var executeCmd = &cobra.Command{
	Use:   "execute [task]",
	Short: "Execute a task using AI without interactive mode",
	Long: `Execute a task using AI without entering interactive chat mode.

This command allows you to run one-off tasks and get results immediately.
The AI will analyze your request, plan the execution, and run the necessary
commands to complete the task.

Examples:
  arien-ai execute "list all running processes"
  arien-ai execute "check disk usage and clean up old files"
  arien-ai execute "find all Go files in this project"
  arien-ai execute "analyze system performance"`,
	Args: cobra.ExactArgs(1),
	RunE: runExecute,
}

func init() {
	rootCmd.AddCommand(executeCmd)
	
	// Execute-specific flags
	executeCmd.Flags().Bool("dry-run", false, "Show what would be executed without running commands")
	executeCmd.Flags().Bool("auto-confirm", false, "Automatically confirm dangerous operations")
	executeCmd.Flags().String("output-format", "text", "Output format (text, json, yaml)")
	executeCmd.Flags().Bool("verbose", false, "Show detailed execution information")
	executeCmd.Flags().Int("timeout", 300, "Maximum execution time in seconds")
}

func runExecute(cmd *cobra.Command, args []string) error {
	task := args[0]
	
	// Get configuration
	if cfg == nil {
		return fmt.Errorf("configuration not loaded")
	}
	
	// Get flags
	providerName, _ := cmd.Flags().GetString("provider")
	modelName, _ := cmd.Flags().GetString("model")
	dryRun, _ := cmd.Flags().GetBool("dry-run")
	autoConfirm, _ := cmd.Flags().GetBool("auto-confirm")
	outputFormat, _ := cmd.Flags().GetString("output-format")
	verbose, _ := cmd.Flags().GetBool("verbose")
	timeoutSecs, _ := cmd.Flags().GetInt("timeout")
	debug, _ := cmd.Flags().GetBool("debug")
	
	// Create AI provider
	var provider providers.AIProvider
	
	switch providerName {
	case "deepseek":
		providerConfig := providers.ProviderConfig{
			Name:       "deepseek",
			BaseURL:    cfg.AI.Providers.Deepseek.BaseURL,
			APIKey:     cfg.AI.Providers.Deepseek.APIKey,
			Timeout:    cfg.AI.Providers.Deepseek.Timeout,
			MaxRetries: cfg.AI.Providers.Deepseek.MaxRetries,
			Headers:    cfg.AI.Providers.Deepseek.Headers,
		}
		provider = providers.NewDeepseekProvider(providerConfig)
		
		if modelName == "" {
			modelName = cfg.AI.Providers.Deepseek.Models.Chat
		}
		
	case "ollama":
		providerConfig := providers.ProviderConfig{
			Name:       "ollama",
			BaseURL:    cfg.AI.Providers.Ollama.BaseURL,
			Timeout:    cfg.AI.Providers.Ollama.Timeout,
			MaxRetries: cfg.AI.Providers.Ollama.MaxRetries,
			Headers:    cfg.AI.Providers.Ollama.Headers,
		}
		provider = providers.NewOllamaProvider(providerConfig)

		if modelName == "" {
			modelName = cfg.AI.Providers.Ollama.Models.Default
		}
		
	default:
		return fmt.Errorf("unsupported provider: %s", providerName)
	}
	
	// Test provider health
	ctx := context.Background()
	if err := provider.IsHealthy(ctx); err != nil {
		return fmt.Errorf("provider health check failed: %w", err)
	}
	
	// Create shell executor
	shellConfig := shell.GetDefaultConfig()
	shellConfig.RequireConfirmation = cfg.Security.RequireConfirmation && !autoConfirm
	shellConfig.DangerousCommands = cfg.Security.DangerousCommands
	shellConfig.MaxExecutionTime = cfg.Security.MaxExecutionTime
	shellConfig.SandboxMode = cfg.Security.SandboxMode
	shellConfig.AllowedDirectories = cfg.Security.AllowedDirectories
	
	executor := shell.NewExecutor(shellConfig)
	
	// Create function calling registry
	registry := function_calling.NewRegistry()
	
	// Register tools
	if err := registerTools(registry, executor); err != nil {
		return fmt.Errorf("failed to register tools: %w", err)
	}
	
	// Show spinner while processing
	spinner := components.NewLoadingSpinner(components.BallType, "Processing task...")
	
	if verbose {
		fmt.Printf("🤖 Arien-AI Execute Mode\n")
		fmt.Printf("Task: %s\n", task)
		fmt.Printf("Provider: %s\n", providerName)
		fmt.Printf("Model: %s\n", modelName)
		fmt.Printf("Dry Run: %t\n", dryRun)
		fmt.Printf("Auto Confirm: %t\n", autoConfirm)
		fmt.Printf("Output Format: %s\n", outputFormat)
		fmt.Printf("\n")
	}
	
	// Start spinner in a goroutine
	go func() {
		for {
			fmt.Printf("\r%s", spinner.View())
			time.Sleep(100 * time.Millisecond)
		}
	}()
	
	// Create system prompt
	systemPrompt := prompts.BuildCompleteSystemPrompt(true, true, true)
	if dryRun {
		systemPrompt += "\n\nIMPORTANT: This is a DRY RUN. Do not actually execute any commands. Instead, explain what you would do and show the commands you would run."
	}
	
	// Prepare messages
	messages := []providers.Message{
		{
			Role:    "system",
			Content: systemPrompt,
		},
		{
			Role:    "user",
			Content: task,
		},
	}
	
	// Prepare tools for AI
	tools := []providers.Tool{}
	if !dryRun {
		aiTools := registry.GetToolsForAI()
		tools = make([]providers.Tool, len(aiTools))
		for i, tool := range aiTools {
			tools[i] = providers.Tool{
				Type: "function",
				Function: providers.ToolFunction{
					Name:        tool["function"].(map[string]interface{})["name"].(string),
					Description: tool["function"].(map[string]interface{})["description"].(string),
					Parameters:  tool["function"].(map[string]interface{})["parameters"].(map[string]interface{}),
				},
			}
		}
	}
	
	// Create request
	request := &providers.ChatRequest{
		Model:       modelName,
		Messages:    messages,
		Tools:       tools,
		ToolChoice:  "auto",
		Temperature: 0.1, // Lower temperature for more deterministic execution
		MaxTokens:   4000,
	}
	
	// Create context with timeout
	execCtx, cancel := context.WithTimeout(ctx, time.Duration(timeoutSecs)*time.Second)
	defer cancel()
	
	// Send request to AI
	response, err := provider.Chat(execCtx, request)
	if err != nil {
		fmt.Printf("\r") // Clear spinner
		return fmt.Errorf("AI request failed: %w", err)
	}
	
	fmt.Printf("\r") // Clear spinner
	
	if len(response.Choices) == 0 {
		return fmt.Errorf("no response from AI")
	}
	
	choice := response.Choices[0]
	
	// Display AI response
	if choice.Message.Content != "" {
		fmt.Printf("🤖 AI Response:\n%s\n\n", choice.Message.Content)
	}
	
	// Execute tool calls if any
	if len(choice.ToolCalls) > 0 && !dryRun {
		fmt.Printf("🔧 Executing tools:\n")
		
		for i, toolCall := range choice.ToolCalls {
			fmt.Printf("%d. %s\n", i+1, toolCall.Function.Name)
			
			if debug {
				fmt.Printf("   Arguments: %s\n", toolCall.Function.Arguments)
			}
			
			// Parse arguments
			var args map[string]interface{}
			if err := json.Unmarshal([]byte(toolCall.Function.Arguments), &args); err != nil {
				fmt.Printf("   ❌ Error parsing arguments: %v\n", err)
				continue
			}
			
			// Execute tool
			result, err := registry.ExecuteTool(execCtx, toolCall.Function.Name, args)
			if err != nil {
				fmt.Printf("   ❌ Error executing tool: %v\n", err)
				continue
			}
			
			// Display result
			if result.Success {
				fmt.Printf("   ✅ Success\n")
				if result.Output != "" {
					if verbose {
						fmt.Printf("   Output:\n%s\n", indentText(result.Output, "   "))
					} else {
						// Show truncated output
						lines := strings.Split(result.Output, "\n")
						if len(lines) > 10 {
							fmt.Printf("   Output (first 10 lines):\n%s\n   ... (%d more lines)\n", 
								indentText(strings.Join(lines[:10], "\n"), "   "), len(lines)-10)
						} else {
							fmt.Printf("   Output:\n%s\n", indentText(result.Output, "   "))
						}
					}
				}
			} else {
				fmt.Printf("   ❌ Failed: %s\n", result.Error)
			}
			
			fmt.Println()
		}
	} else if len(choice.ToolCalls) > 0 && dryRun {
		fmt.Printf("🔍 Would execute tools (dry run):\n")
		for i, toolCall := range choice.ToolCalls {
			fmt.Printf("%d. %s\n", i+1, toolCall.Function.Name)
			fmt.Printf("   Arguments: %s\n", toolCall.Function.Arguments)
		}
		fmt.Println()
	}
	
	// Format output based on requested format
	switch outputFormat {
	case "json":
		return outputJSON(choice, response.Usage)
	case "yaml":
		return outputYAML(choice, response.Usage)
	default:
		// Text format already handled above
	}
	
	if verbose {
		fmt.Printf("📊 Usage: %d prompt tokens, %d completion tokens, %d total tokens\n",
			response.Usage.PromptTokens, response.Usage.CompletionTokens, response.Usage.TotalTokens)
	}
	
	return nil
}



// indentText indents each line of text with the given prefix
func indentText(text, prefix string) string {
	lines := strings.Split(text, "\n")
	for i, line := range lines {
		lines[i] = prefix + line
	}
	return strings.Join(lines, "\n")
}

// outputJSON outputs the response in JSON format
func outputJSON(choice providers.Choice, usage providers.Usage) error {
	output := map[string]interface{}{
		"response": choice.Message.Content,
		"tool_calls": choice.ToolCalls,
		"usage": usage,
	}
	
	jsonBytes, err := json.MarshalIndent(output, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal JSON: %w", err)
	}
	
	fmt.Println(string(jsonBytes))
	return nil
}

// outputYAML outputs the response in YAML format
func outputYAML(choice providers.Choice, usage providers.Usage) error {
	// Simple YAML output (could use a proper YAML library)
	fmt.Printf("response: |\n%s\n", indentText(choice.Message.Content, "  "))
	
	if len(choice.ToolCalls) > 0 {
		fmt.Printf("tool_calls:\n")
		for i, tc := range choice.ToolCalls {
			fmt.Printf("  - name: %s\n", tc.Function.Name)
			fmt.Printf("    arguments: %s\n", tc.Function.Arguments)
			if i < len(choice.ToolCalls)-1 {
				fmt.Println()
			}
		}
	}
	
	fmt.Printf("usage:\n")
	fmt.Printf("  prompt_tokens: %d\n", usage.PromptTokens)
	fmt.Printf("  completion_tokens: %d\n", usage.CompletionTokens)
	fmt.Printf("  total_tokens: %d\n", usage.TotalTokens)
	
	return nil
}
