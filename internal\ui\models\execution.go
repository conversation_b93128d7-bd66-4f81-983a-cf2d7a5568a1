package models

import (
	"context"
	"fmt"
	"strings"
	"time"

	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"

	"arien-ai/internal/ai/function_calling"
	"arien-ai/internal/ai/providers"
	"arien-ai/internal/config"
	"arien-ai/internal/shell"
	"arien-ai/internal/ui/components"
)

// ExecutionConfig contains configuration for the execution model
type ExecutionConfig struct {
	Provider        providers.AIProvider
	Model          string
	Registry       *function_calling.Registry
	Executor       *function_calling.Executor
	ShellExecutor  *shell.Executor
	UIConfig       config.UIConfig
	SystemPrompt   string
	AutoExecute    bool
	ShowProgress   bool
	Debug          bool
}

// ExecutionModel represents the command execution interface
type ExecutionModel struct {
	config       ExecutionConfig
	command      string
	status       ExecutionStatus
	steps        []ExecutionStep
	currentStep  int
	progress     *components.MultiProgressBar
	spinner      components.LoadingSpinner
	output       strings.Builder
	err          error
	width        int
	height       int
	styles       ExecutionStyles
	startTime    time.Time
}

// ExecutionStatus represents the current execution status
type ExecutionStatus int

const (
	StatusIdle ExecutionStatus = iota
	StatusPlanning
	StatusExecuting
	StatusCompleted
	StatusFailed
)

// ExecutionStep represents a single execution step
type ExecutionStep struct {
	ID          string
	Name        string
	Description string
	Status      StepStatus
	StartTime   time.Time
	EndTime     time.Time
	Output      string
	Error       string
	ToolCall    *function_calling.ToolCall
}

// StepStatus represents the status of an execution step
type StepStatus int

const (
	StepPending StepStatus = iota
	StepRunning
	StepCompleted
	StepFailed
	StepSkipped
)

// ExecutionStyles contains styling for the execution interface
type ExecutionStyles struct {
	Header       lipgloss.Style
	Command      lipgloss.Style
	Step         lipgloss.Style
	StepRunning  lipgloss.Style
	StepComplete lipgloss.Style
	StepFailed   lipgloss.Style
	Output       lipgloss.Style
	Error        lipgloss.Style
	Progress     lipgloss.Style
	Border       lipgloss.Style
}

// NewExecutionModel creates a new execution model
func NewExecutionModel(config ExecutionConfig, command string) *ExecutionModel {
	// Create progress bar
	progress := components.NewMultiProgressBar("Execution Progress")
	
	// Create spinner
	spinner := components.NewLoadingSpinner(components.BallType, "Initializing...")
	
	// Create styles
	styles := createExecutionStyles(config.UIConfig)
	
	return &ExecutionModel{
		config:      config,
		command:     command,
		status:      StatusIdle,
		steps:       []ExecutionStep{},
		currentStep: -1,
		progress:    progress,
		spinner:     spinner,
		styles:      styles,
		startTime:   time.Now(),
	}
}

// Init initializes the execution model
func (m *ExecutionModel) Init() tea.Cmd {
	return tea.Batch(
		m.spinner.Init(),
		m.startExecution(),
	)
}

// Update handles messages and updates the model
func (m *ExecutionModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	var cmds []tea.Cmd
	
	switch msg := msg.(type) {
	case tea.WindowSizeMsg:
		m.width = msg.Width
		m.height = msg.Height
		
	case tea.KeyMsg:
		switch msg.Type {
		case tea.KeyCtrlC, tea.KeyEsc:
			return m, tea.Quit
		case tea.KeyEnter:
			if m.status == StatusCompleted || m.status == StatusFailed {
				return m, tea.Quit
			}
		}
		
	case executionPlanMsg:
		m.status = StatusExecuting
		m.steps = msg.steps
		m.setupProgressBars()
		return m, m.executeSteps()
		
	case stepStartMsg:
		m.updateStepStatus(msg.stepID, StepRunning)
		m.currentStep = msg.stepIndex
		m.spinner.SetMessage(fmt.Sprintf("Executing: %s", msg.stepName))
		
	case stepCompleteMsg:
		m.updateStepStatus(msg.stepID, StepCompleted)
		m.updateStepResult(msg.stepID, msg.result)
		if bar := m.progress.GetBar(msg.stepID); bar != nil {
			bar.Increment(1)
		}
		
	case stepFailedMsg:
		m.updateStepStatus(msg.stepID, StepFailed)
		m.updateStepError(msg.stepID, msg.error)
		
	case executionCompleteMsg:
		m.status = StatusCompleted
		m.spinner.SetMessage("Execution completed")
		
	case executionFailedMsg:
		m.status = StatusFailed
		m.err = msg.error
		m.spinner.SetMessage("Execution failed")
		
	default:
		var cmd tea.Cmd
		m.spinner, cmd = m.spinner.Update(msg)
		cmds = append(cmds, cmd)
	}
	
	return m, tea.Batch(cmds...)
}

// View renders the execution interface
func (m *ExecutionModel) View() string {
	if m.width == 0 {
		return "Loading..."
	}
	
	var content strings.Builder
	
	// Header
	header := m.styles.Header.Render("Arien-AI Command Execution")
	content.WriteString(header)
	content.WriteString("\n\n")
	
	// Command
	commandDisplay := m.styles.Command.Render(fmt.Sprintf("Command: %s", m.command))
	content.WriteString(commandDisplay)
	content.WriteString("\n\n")
	
	// Status
	statusText := m.getStatusText()
	content.WriteString(statusText)
	content.WriteString("\n\n")
	
	// Progress
	if m.status == StatusExecuting && len(m.steps) > 0 {
		content.WriteString(m.progress.View())
		content.WriteString("\n\n")
	}
	
	// Current spinner
	if m.status == StatusPlanning || m.status == StatusExecuting {
		content.WriteString(m.spinner.View())
		content.WriteString("\n\n")
	}
	
	// Steps
	if len(m.steps) > 0 {
		content.WriteString(m.renderSteps())
		content.WriteString("\n\n")
	}
	
	// Output
	if m.output.Len() > 0 {
		content.WriteString(m.styles.Border.Render("Output:\n" + m.output.String()))
		content.WriteString("\n\n")
	}
	
	// Error
	if m.err != nil {
		content.WriteString(m.styles.Error.Render(fmt.Sprintf("Error: %s", m.err.Error())))
		content.WriteString("\n\n")
	}
	
	// Footer
	footer := m.getFooter()
	content.WriteString(footer)
	
	return content.String()
}

// startExecution starts the execution process
func (m *ExecutionModel) startExecution() tea.Cmd {
	return func() tea.Msg {
		m.status = StatusPlanning
		
		// Create execution plan using AI
		ctx := context.Background()
		
		// Prepare messages for AI
		messages := []providers.Message{
			{
				Role: "system",
				Content: `You are an AI assistant that helps execute commands by breaking them down into steps.
				
Your task is to analyze the user's command and create an execution plan using the available tools.
You should:
1. Break down complex commands into logical steps
2. Use appropriate tools for each step
3. Ensure steps are executed in the correct order
4. Handle errors gracefully

Available tools: shell_execute, file_operations, system_info, process_management`,
			},
			{
				Role:    "user",
				Content: fmt.Sprintf("Please create an execution plan for this command: %s", m.command),
			},
		}
		
		// Get tools
		var tools []providers.Tool
		if m.config.Registry != nil {
			toolsForAI := m.config.Registry.GetToolsForAI()
			tools = make([]providers.Tool, len(toolsForAI))
			for i, tool := range toolsForAI {
				tools[i] = providers.Tool{
					Type: "function",
					Function: providers.ToolFunction{
						Name:        tool["function"].(map[string]interface{})["name"].(string),
						Description: tool["function"].(map[string]interface{})["description"].(string),
						Parameters:  tool["function"].(map[string]interface{})["parameters"].(map[string]interface{}),
					},
				}
			}
		}
		
		// Send request
		request := &providers.ChatRequest{
			Model:       m.config.Model,
			Messages:    messages,
			Tools:       tools,
			ToolChoice:  "auto",
			Temperature: 0.1, // Low temperature for consistent planning
		}
		
		response, err := m.config.Provider.Chat(ctx, request)
		if err != nil {
			return executionFailedMsg{error: err}
		}
		
		if len(response.Choices) == 0 {
			return executionFailedMsg{error: fmt.Errorf("no response from AI")}
		}
		
		// Parse tool calls into execution steps
		choice := response.Choices[0]
		steps := []ExecutionStep{}
		
		for i, toolCall := range choice.ToolCalls {
			step := ExecutionStep{
				ID:          fmt.Sprintf("step_%d", i+1),
				Name:        toolCall.Function.Name,
				Description: fmt.Sprintf("Execute %s", toolCall.Function.Name),
				Status:      StepPending,
				ToolCall: &function_calling.ToolCall{
					ID:        toolCall.ID,
					Name:      toolCall.Function.Name,
					Arguments: toolCall.Function.Arguments,
				},
			}
			steps = append(steps, step)
		}
		
		if len(steps) == 0 {
			// If no tool calls, create a simple shell execution step
			step := ExecutionStep{
				ID:          "step_1",
				Name:        "shell_execute",
				Description: fmt.Sprintf("Execute command: %s", m.command),
				Status:      StepPending,
				ToolCall: &function_calling.ToolCall{
					ID:        "shell_1",
					Name:      "shell_execute",
					Arguments: fmt.Sprintf(`{"command": "%s"}`, m.command),
				},
			}
			steps = append(steps, step)
		}
		
		return executionPlanMsg{steps: steps}
	}
}

// executeSteps executes all steps in sequence
func (m *ExecutionModel) executeSteps() tea.Cmd {
	return func() tea.Msg {
		ctx := context.Background()
		
		for _, step := range m.steps {
			// Execute step
			execCtx := &function_calling.ExecutionContext{
				UserID:    "user",
				SessionID: "session",
				RequestID: fmt.Sprintf("req_%d", time.Now().Unix()),
			}

			result, err := m.config.Executor.ExecuteSingle(ctx, step.ToolCall, execCtx)
			if err != nil {
				return executionFailedMsg{error: err}
			}

			// Add output
			if result.Result != nil && result.Result.Output != "" {
				m.output.WriteString(fmt.Sprintf("[%s] %s\n", step.Name, result.Result.Output))
			}
		}
		
		return executionCompleteMsg{}
	}
}

// Message types for execution
type executionPlanMsg struct {
	steps []ExecutionStep
}

type stepStartMsg struct {
	stepID    string
	stepIndex int
	stepName  string
}

type stepCompleteMsg struct {
	stepID string
	result *function_calling.ExecutionResult
}

type stepFailedMsg struct {
	stepID string
	error  string
}

type executionCompleteMsg struct{}

type executionFailedMsg struct {
	error error
}

// Helper methods

func (m *ExecutionModel) getStatusText() string {
	switch m.status {
	case StatusIdle:
		return "Ready to execute"
	case StatusPlanning:
		return "Planning execution..."
	case StatusExecuting:
		return fmt.Sprintf("Executing step %d of %d", m.currentStep+1, len(m.steps))
	case StatusCompleted:
		duration := time.Since(m.startTime)
		return m.styles.StepComplete.Render(fmt.Sprintf("✓ Execution completed in %s", duration.Round(time.Millisecond)))
	case StatusFailed:
		return m.styles.StepFailed.Render("✗ Execution failed")
	default:
		return "Unknown status"
	}
}

func (m *ExecutionModel) getFooter() string {
	if m.status == StatusCompleted || m.status == StatusFailed {
		return "Press Enter or Esc to exit"
	}
	return "Press Ctrl+C to cancel"
}

func (m *ExecutionModel) setupProgressBars() {
	for _, step := range m.steps {
		m.progress.AddBar(step.ID, 1, 30, step.Description)
	}
}

func (m *ExecutionModel) updateStepStatus(stepID string, status StepStatus) {
	for i := range m.steps {
		if m.steps[i].ID == stepID {
			m.steps[i].Status = status
			if status == StepRunning {
				m.steps[i].StartTime = time.Now()
			} else if status == StepCompleted || status == StepFailed {
				m.steps[i].EndTime = time.Now()
			}
			break
		}
	}
}

func (m *ExecutionModel) updateStepResult(stepID string, result *function_calling.ExecutionResult) {
	for i := range m.steps {
		if m.steps[i].ID == stepID {
			if result.Result != nil {
				m.steps[i].Output = result.Result.Output
			}
			break
		}
	}
}

func (m *ExecutionModel) updateStepError(stepID string, errorMsg string) {
	for i := range m.steps {
		if m.steps[i].ID == stepID {
			m.steps[i].Error = errorMsg
			break
		}
	}
}

func (m *ExecutionModel) renderSteps() string {
	var steps strings.Builder
	steps.WriteString("Execution Steps:\n")
	
	for i, step := range m.steps {
		var icon, style string
		switch step.Status {
		case StepPending:
			icon = "○"
			style = "pending"
		case StepRunning:
			icon = "●"
			style = "running"
		case StepCompleted:
			icon = "✓"
			style = "completed"
		case StepFailed:
			icon = "✗"
			style = "failed"
		case StepSkipped:
			icon = "⊘"
			style = "skipped"
		}
		
		stepText := fmt.Sprintf("%s %d. %s", icon, i+1, step.Description)
		
		switch style {
		case "running":
			stepText = m.styles.StepRunning.Render(stepText)
		case "completed":
			stepText = m.styles.StepComplete.Render(stepText)
		case "failed":
			stepText = m.styles.StepFailed.Render(stepText)
		default:
			stepText = m.styles.Step.Render(stepText)
		}
		
		steps.WriteString(stepText)
		
		if step.Error != "" {
			steps.WriteString("\n  " + m.styles.Error.Render("Error: "+step.Error))
		}
		
		steps.WriteString("\n")
	}
	
	return steps.String()
}

func createExecutionStyles(uiConfig config.UIConfig) ExecutionStyles {
	return ExecutionStyles{
		Header: lipgloss.NewStyle().
			Foreground(lipgloss.Color(uiConfig.Colors.Primary)).
			Bold(true).
			Underline(true),
		Command: lipgloss.NewStyle().
			Foreground(lipgloss.Color(uiConfig.Colors.Secondary)).
			Bold(true),
		Step: lipgloss.NewStyle().
			Foreground(lipgloss.Color("#ffffff")),
		StepRunning: lipgloss.NewStyle().
			Foreground(lipgloss.Color(uiConfig.Colors.Warning)).
			Bold(true),
		StepComplete: lipgloss.NewStyle().
			Foreground(lipgloss.Color(uiConfig.Colors.Success)).
			Bold(true),
		StepFailed: lipgloss.NewStyle().
			Foreground(lipgloss.Color(uiConfig.Colors.Error)).
			Bold(true),
		Output: lipgloss.NewStyle().
			Foreground(lipgloss.Color("#cccccc")),
		Error: lipgloss.NewStyle().
			Foreground(lipgloss.Color(uiConfig.Colors.Error)).
			Bold(true),
		Progress: lipgloss.NewStyle().
			Foreground(lipgloss.Color(uiConfig.Colors.Primary)),
		Border: lipgloss.NewStyle().
			Border(lipgloss.RoundedBorder()).
			BorderForeground(lipgloss.Color(uiConfig.Colors.Primary)).
			Padding(1),
	}
}
