package prompts

import (
	"fmt"
	"strings"
	"text/template"
	"time"
)

// PromptTemplate represents a reusable prompt template
type PromptTemplate struct {
	Name        string
	Description string
	Template    string
	Variables   []TemplateVariable
}

// TemplateVariable represents a variable in a prompt template
type TemplateVariable struct {
	Name        string
	Type        string
	Description string
	Required    bool
	De<PERSON>ult     interface{}
}

// TemplateData contains data for template rendering
type TemplateData struct {
	Variables map[string]interface{}
	Context   TemplateContext
}

// TemplateContext provides context information for templates
type TemplateContext struct {
	Timestamp   time.Time
	OS          string
	User        string
	WorkingDir  string
	SessionID   string
	RequestID   string
	TaskType    string
	SafetyLevel string
}

// Predefined prompt templates
var (
	// CommandExecutionTemplate for command execution tasks
	CommandExecutionTemplate = PromptTemplate{
		Name:        "command_execution",
		Description: "Template for executing shell commands safely",
		Template: `You are about to execute a command for the user. Please follow these guidelines:

COMMAND: {{.Variables.command}}
WORKING_DIRECTORY: {{.Context.WorkingDir}}
SAFETY_LEVEL: {{.Context.SafetyLevel}}

EXECUTION CONTEXT:
- Current OS: {{.Context.OS}}
- Timestamp: {{.Context.Timestamp.Format "2006-01-02 15:04:05"}}
- Session ID: {{.Context.SessionID}}

SAFETY CHECKS:
{{if eq .Context.SafetyLevel "high"}}
- This is a HIGH RISK operation
- Confirm with user before proceeding
- Validate all parameters carefully
{{else if eq .Context.SafetyLevel "medium"}}
- This is a MEDIUM RISK operation
- Proceed with caution
- Validate parameters
{{else}}
- This is a LOW RISK operation
- Proceed normally
{{end}}

INSTRUCTIONS:
1. Analyze the command for potential risks
2. Execute using appropriate tools
3. Capture and process output
4. Report results clearly
5. Handle any errors gracefully

{{if .Variables.additional_context}}
ADDITIONAL CONTEXT: {{.Variables.additional_context}}
{{end}}`,
		Variables: []TemplateVariable{
			{Name: "command", Type: "string", Description: "The command to execute", Required: true},
			{Name: "additional_context", Type: "string", Description: "Additional context for execution", Required: false},
		},
	}

	// FileOperationTemplate for file operations
	FileOperationTemplate = PromptTemplate{
		Name:        "file_operation",
		Description: "Template for file operations",
		Template: `You are about to perform a file operation. Please follow these guidelines:

OPERATION: {{.Variables.operation}}
FILE_PATH: {{.Variables.file_path}}
{{if .Variables.destination}}DESTINATION: {{.Variables.destination}}{{end}}

EXECUTION CONTEXT:
- Current OS: {{.Context.OS}}
- Working Directory: {{.Context.WorkingDir}}
- Safety Level: {{.Context.SafetyLevel}}

SAFETY CONSIDERATIONS:
- Validate file paths for security
- Check file permissions before operations
- Backup important files when modifying
- Use appropriate error handling

INSTRUCTIONS:
1. Validate the file path and operation
2. Check permissions and accessibility
3. Execute the operation safely
4. Verify the result
5. Report status and any issues

{{if .Variables.backup_required}}
BACKUP REQUIRED: Create a backup before modifying the file
{{end}}`,
		Variables: []TemplateVariable{
			{Name: "operation", Type: "string", Description: "Type of file operation", Required: true},
			{Name: "file_path", Type: "string", Description: "Path to the file", Required: true},
			{Name: "destination", Type: "string", Description: "Destination path for copy/move operations", Required: false},
			{Name: "backup_required", Type: "bool", Description: "Whether to create a backup", Required: false, Default: false},
		},
	}

	// SystemDiagnosisTemplate for system diagnosis
	SystemDiagnosisTemplate = PromptTemplate{
		Name:        "system_diagnosis",
		Description: "Template for system diagnosis and troubleshooting",
		Template: `You are performing system diagnosis. Please follow this systematic approach:

DIAGNOSIS TARGET: {{.Variables.target}}
SYMPTOMS: {{.Variables.symptoms}}
PRIORITY: {{.Variables.priority}}

EXECUTION CONTEXT:
- System: {{.Context.OS}}
- Timestamp: {{.Context.Timestamp.Format "2006-01-02 15:04:05"}}
- Session: {{.Context.SessionID}}

DIAGNOSTIC APPROACH:
1. INFORMATION GATHERING
   - Collect system information
   - Check resource usage
   - Review relevant logs
   - Identify running processes

2. ANALYSIS
   - Correlate symptoms with findings
   - Identify potential root causes
   - Prioritize investigation areas

3. TESTING
   - Test hypotheses systematically
   - Use targeted diagnostic commands
   - Verify findings

4. REPORTING
   - Summarize findings clearly
   - Provide actionable recommendations
   - Suggest preventive measures

{{if .Variables.focus_areas}}
FOCUS AREAS: {{range .Variables.focus_areas}}
- {{.}}{{end}}
{{end}}

Remember to be thorough but efficient in your diagnosis.`,
		Variables: []TemplateVariable{
			{Name: "target", Type: "string", Description: "What to diagnose", Required: true},
			{Name: "symptoms", Type: "string", Description: "Observed symptoms", Required: true},
			{Name: "priority", Type: "string", Description: "Priority level", Required: false, Default: "medium"},
			{Name: "focus_areas", Type: "[]string", Description: "Specific areas to focus on", Required: false},
		},
	}

	// SecurityAuditTemplate for security audits
	SecurityAuditTemplate = PromptTemplate{
		Name:        "security_audit",
		Description: "Template for security auditing tasks",
		Template: `You are performing a security audit. Follow these security-focused guidelines:

AUDIT SCOPE: {{.Variables.scope}}
AUDIT_TYPE: {{.Variables.audit_type}}

SECURITY CONTEXT:
- System: {{.Context.OS}}
- Safety Level: CRITICAL
- Audit Session: {{.Context.SessionID}}
- Timestamp: {{.Context.Timestamp.Format "2006-01-02 15:04:05"}}

SECURITY AUDIT CHECKLIST:
1. PERMISSIONS AND ACCESS
   - File and directory permissions
   - User accounts and groups
   - Service account configurations
   - SSH key management

2. SYSTEM CONFIGURATION
   - Security settings and policies
   - Network configuration
   - Firewall rules
   - Service configurations

3. MONITORING AND LOGGING
   - Log file analysis
   - Security event monitoring
   - Audit trail verification
   - Anomaly detection

4. VULNERABILITY ASSESSMENT
   - Software version checks
   - Security patch status
   - Configuration vulnerabilities
   - Exposed services

SECURITY PRINCIPLES:
- Principle of least privilege
- Defense in depth
- Fail securely
- Keep security simple
- Don't trust, verify

{{if .Variables.compliance_framework}}
COMPLIANCE FRAMEWORK: {{.Variables.compliance_framework}}
{{end}}

IMPORTANT: Be extra cautious and request confirmation for any changes.`,
		Variables: []TemplateVariable{
			{Name: "scope", Type: "string", Description: "Scope of the security audit", Required: true},
			{Name: "audit_type", Type: "string", Description: "Type of audit (configuration, vulnerability, etc.)", Required: true},
			{Name: "compliance_framework", Type: "string", Description: "Compliance framework to follow", Required: false},
		},
	}
)

// TemplateManager manages prompt templates
type TemplateManager struct {
	templates map[string]PromptTemplate
}

// NewTemplateManager creates a new template manager
func NewTemplateManager() *TemplateManager {
	tm := &TemplateManager{
		templates: make(map[string]PromptTemplate),
	}
	
	// Register default templates
	tm.RegisterTemplate(CommandExecutionTemplate)
	tm.RegisterTemplate(FileOperationTemplate)
	tm.RegisterTemplate(SystemDiagnosisTemplate)
	tm.RegisterTemplate(SecurityAuditTemplate)
	
	return tm
}

// RegisterTemplate registers a new template
func (tm *TemplateManager) RegisterTemplate(template PromptTemplate) {
	tm.templates[template.Name] = template
}

// GetTemplate retrieves a template by name
func (tm *TemplateManager) GetTemplate(name string) (PromptTemplate, bool) {
	template, exists := tm.templates[name]
	return template, exists
}

// ListTemplates returns all available template names
func (tm *TemplateManager) ListTemplates() []string {
	names := make([]string, 0, len(tm.templates))
	for name := range tm.templates {
		names = append(names, name)
	}
	return names
}

// RenderTemplate renders a template with the provided data
func (tm *TemplateManager) RenderTemplate(name string, data TemplateData) (string, error) {
	templateDef, exists := tm.GetTemplate(name)
	if !exists {
		return "", fmt.Errorf("template %s not found", name)
	}
	
	// Validate required variables
	if err := tm.validateTemplateData(templateDef, data); err != nil {
		return "", fmt.Errorf("template validation failed: %w", err)
	}
	
	// Create template
	tmpl, err := template.New(name).Parse(templateDef.Template)
	if err != nil {
		return "", fmt.Errorf("failed to parse template: %w", err)
	}
	
	// Render template
	var result strings.Builder
	if err := tmpl.Execute(&result, data); err != nil {
		return "", fmt.Errorf("failed to execute template: %w", err)
	}
	
	return result.String(), nil
}

// validateTemplateData validates that required variables are provided
func (tm *TemplateManager) validateTemplateData(template PromptTemplate, data TemplateData) error {
	if data.Variables == nil {
		data.Variables = make(map[string]interface{})
	}
	
	for _, variable := range template.Variables {
		if variable.Required {
			if _, exists := data.Variables[variable.Name]; !exists {
				return fmt.Errorf("required variable %s is missing", variable.Name)
			}
		} else if variable.Default != nil {
			if _, exists := data.Variables[variable.Name]; !exists {
				data.Variables[variable.Name] = variable.Default
			}
		}
	}
	
	return nil
}

// CreateTemplateData creates template data with context
func CreateTemplateData(variables map[string]interface{}, context TemplateContext) TemplateData {
	if variables == nil {
		variables = make(map[string]interface{})
	}
	
	return TemplateData{
		Variables: variables,
		Context:   context,
	}
}

// CreateDefaultContext creates a default template context
func CreateDefaultContext(os, user, workingDir, taskType, safetyLevel string) TemplateContext {
	return TemplateContext{
		Timestamp:   time.Now(),
		OS:          os,
		User:        user,
		WorkingDir:  workingDir,
		SessionID:   generateSessionID(),
		RequestID:   generateRequestID(),
		TaskType:    taskType,
		SafetyLevel: safetyLevel,
	}
}

// generateSessionID generates a unique session ID
func generateSessionID() string {
	return fmt.Sprintf("session_%d", time.Now().Unix())
}

// generateRequestID generates a unique request ID
func generateRequestID() string {
	return fmt.Sprintf("req_%d_%d", time.Now().Unix(), time.Now().Nanosecond())
}

// GetTemplateForTask returns the most appropriate template for a task
func (tm *TemplateManager) GetTemplateForTask(taskType string) string {
	switch strings.ToLower(taskType) {
	case "command", "execute", "shell":
		return "command_execution"
	case "file", "files", "filesystem":
		return "file_operation"
	case "diagnosis", "troubleshoot", "debug":
		return "system_diagnosis"
	case "security", "audit", "hardening":
		return "security_audit"
	default:
		return "command_execution" // Default fallback
	}
}

// RenderTaskPrompt renders a prompt for a specific task type
func (tm *TemplateManager) RenderTaskPrompt(taskType string, variables map[string]interface{}, context TemplateContext) (string, error) {
	templateName := tm.GetTemplateForTask(taskType)
	data := CreateTemplateData(variables, context)
	return tm.RenderTemplate(templateName, data)
}

// GetTemplateVariables returns the variables for a template
func (tm *TemplateManager) GetTemplateVariables(name string) ([]TemplateVariable, error) {
	template, exists := tm.GetTemplate(name)
	if !exists {
		return nil, fmt.Errorf("template %s not found", name)
	}
	return template.Variables, nil
}

// ValidateTemplate validates a template definition
func ValidateTemplate(tmpl PromptTemplate) error {
	if tmpl.Name == "" {
		return fmt.Errorf("template name cannot be empty")
	}

	if tmpl.Template == "" {
		return fmt.Errorf("template content cannot be empty")
	}

	// Try to parse the template
	_, err := template.New("validation").Parse(tmpl.Template)
	if err != nil {
		return fmt.Errorf("invalid template syntax: %w", err)
	}

	return nil
}
