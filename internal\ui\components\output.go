package components

import (
	"fmt"
	"strings"
	"time"

	"github.com/charmbracelet/lipgloss"
	"github.com/fatih/color"
)

// OutputFormatter handles formatting of various types of output
type OutputFormatter struct {
	styles OutputStyles
	config OutputConfig
}

// OutputStyles contains styling for different output types
type OutputStyles struct {
	Success    lipgloss.Style
	Error      lipgloss.Style
	Warning    lipgloss.Style
	Info       lipgloss.Style
	Debug      lipgloss.Style
	Command    lipgloss.Style
	Output     lipgloss.Style
	Timestamp  lipgloss.Style
	Border     lipgloss.Style
	Header     lipgloss.Style
}

// OutputConfig contains configuration for output formatting
type OutputConfig struct {
	ShowTimestamps bool
	ShowBorders    bool
	MaxWidth       int
	IndentSize     int
	ColorEnabled   bool
}

// NewOutputFormatter creates a new output formatter
func NewOutputFormatter(config OutputConfig) *OutputFormatter {
	return &OutputFormatter{
		styles: DefaultOutputStyles(),
		config: config,
	}
}

// DefaultOutputStyles returns default output styles
func DefaultOutputStyles() OutputStyles {
	return OutputStyles{
		Success: lipgloss.NewStyle().
			Foreground(lipgloss.Color("#00ff00")).
			Bold(true),
		Error: lipgloss.NewStyle().
			Foreground(lipgloss.Color("#ff0000")).
			Bold(true),
		Warning: lipgloss.NewStyle().
			Foreground(lipgloss.Color("#ffaa00")).
			Bold(true),
		Info: lipgloss.NewStyle().
			Foreground(lipgloss.Color("#0080ff")).
			Bold(true),
		Debug: lipgloss.NewStyle().
			Foreground(lipgloss.Color("#888888")),
		Command: lipgloss.NewStyle().
			Foreground(lipgloss.Color("#ff00ff")).
			Bold(true),
		Output: lipgloss.NewStyle().
			Foreground(lipgloss.Color("#ffffff")),
		Timestamp: lipgloss.NewStyle().
			Foreground(lipgloss.Color("#666666")),
		Border: lipgloss.NewStyle().
			Border(lipgloss.RoundedBorder()).
			BorderForeground(lipgloss.Color("#444444")).
			Padding(1),
		Header: lipgloss.NewStyle().
			Foreground(lipgloss.Color("#00ff00")).
			Bold(true).
			Underline(true),
	}
}

// FormatSuccess formats a success message
func (of *OutputFormatter) FormatSuccess(message string) string {
	return of.formatMessage("✓", message, of.styles.Success)
}

// FormatError formats an error message
func (of *OutputFormatter) FormatError(message string) string {
	return of.formatMessage("✗", message, of.styles.Error)
}

// FormatWarning formats a warning message
func (of *OutputFormatter) FormatWarning(message string) string {
	return of.formatMessage("⚠", message, of.styles.Warning)
}

// FormatInfo formats an info message
func (of *OutputFormatter) FormatInfo(message string) string {
	return of.formatMessage("ℹ", message, of.styles.Info)
}

// FormatDebug formats a debug message
func (of *OutputFormatter) FormatDebug(message string) string {
	return of.formatMessage("🐛", message, of.styles.Debug)
}

// FormatCommand formats a command
func (of *OutputFormatter) FormatCommand(command string) string {
	return of.formatMessage("$", command, of.styles.Command)
}

// FormatOutput formats command output
func (of *OutputFormatter) FormatOutput(output string) string {
	if output == "" {
		return ""
	}
	
	lines := strings.Split(strings.TrimSpace(output), "\n")
	var formatted strings.Builder
	
	for i, line := range lines {
		if of.config.IndentSize > 0 {
			formatted.WriteString(strings.Repeat(" ", of.config.IndentSize))
		}
		formatted.WriteString(of.styles.Output.Render(line))
		if i < len(lines)-1 {
			formatted.WriteString("\n")
		}
	}
	
	return formatted.String()
}

// FormatHeader formats a header
func (of *OutputFormatter) FormatHeader(title string) string {
	header := of.styles.Header.Render(title)
	if of.config.ShowBorders {
		return of.styles.Border.Render(header)
	}
	return header
}

// FormatSection formats a section with title and content
func (of *OutputFormatter) FormatSection(title, content string) string {
	var section strings.Builder
	
	// Add title
	section.WriteString(of.FormatHeader(title))
	section.WriteString("\n\n")
	
	// Add content with indentation
	if content != "" {
		lines := strings.Split(content, "\n")
		for i, line := range lines {
			if of.config.IndentSize > 0 {
				section.WriteString(strings.Repeat(" ", of.config.IndentSize))
			}
			section.WriteString(line)
			if i < len(lines)-1 {
				section.WriteString("\n")
			}
		}
	}
	
	return section.String()
}

// FormatTable formats data as a table
func (of *OutputFormatter) FormatTable(headers []string, rows [][]string) string {
	if len(headers) == 0 || len(rows) == 0 {
		return ""
	}
	
	// Calculate column widths
	colWidths := make([]int, len(headers))
	for i, header := range headers {
		colWidths[i] = len(header)
	}
	
	for _, row := range rows {
		for i, cell := range row {
			if i < len(colWidths) && len(cell) > colWidths[i] {
				colWidths[i] = len(cell)
			}
		}
	}
	
	var table strings.Builder
	
	// Header
	table.WriteString("┌")
	for i, width := range colWidths {
		table.WriteString(strings.Repeat("─", width+2))
		if i < len(colWidths)-1 {
			table.WriteString("┬")
		}
	}
	table.WriteString("┐\n")
	
	// Header row
	table.WriteString("│")
	for i, header := range headers {
		table.WriteString(fmt.Sprintf(" %-*s │", colWidths[i], header))
	}
	table.WriteString("\n")
	
	// Header separator
	table.WriteString("├")
	for i, width := range colWidths {
		table.WriteString(strings.Repeat("─", width+2))
		if i < len(colWidths)-1 {
			table.WriteString("┼")
		}
	}
	table.WriteString("┤\n")
	
	// Data rows
	for _, row := range rows {
		table.WriteString("│")
		for i, cell := range row {
			if i < len(colWidths) {
				table.WriteString(fmt.Sprintf(" %-*s │", colWidths[i], cell))
			}
		}
		table.WriteString("\n")
	}
	
	// Bottom border
	table.WriteString("└")
	for i, width := range colWidths {
		table.WriteString(strings.Repeat("─", width+2))
		if i < len(colWidths)-1 {
			table.WriteString("┴")
		}
	}
	table.WriteString("┘")
	
	return table.String()
}

// FormatList formats a list of items
func (of *OutputFormatter) FormatList(items []string, numbered bool) string {
	if len(items) == 0 {
		return ""
	}
	
	var list strings.Builder
	
	for i, item := range items {
		if of.config.IndentSize > 0 {
			list.WriteString(strings.Repeat(" ", of.config.IndentSize))
		}
		
		if numbered {
			list.WriteString(fmt.Sprintf("%d. %s", i+1, item))
		} else {
			list.WriteString(fmt.Sprintf("• %s", item))
		}
		
		if i < len(items)-1 {
			list.WriteString("\n")
		}
	}
	
	return list.String()
}

// FormatKeyValue formats key-value pairs
func (of *OutputFormatter) FormatKeyValue(pairs map[string]string) string {
	if len(pairs) == 0 {
		return ""
	}
	
	// Find max key length for alignment
	maxKeyLen := 0
	for key := range pairs {
		if len(key) > maxKeyLen {
			maxKeyLen = len(key)
		}
	}
	
	var kv strings.Builder
	i := 0
	for key, value := range pairs {
		if of.config.IndentSize > 0 {
			kv.WriteString(strings.Repeat(" ", of.config.IndentSize))
		}
		
		kv.WriteString(fmt.Sprintf("%-*s: %s", maxKeyLen, key, value))
		
		if i < len(pairs)-1 {
			kv.WriteString("\n")
		}
		i++
	}
	
	return kv.String()
}

// formatMessage formats a message with icon and timestamp
func (of *OutputFormatter) formatMessage(icon, message string, style lipgloss.Style) string {
	var formatted strings.Builder
	
	// Add timestamp if enabled
	if of.config.ShowTimestamps {
		timestamp := time.Now().Format("15:04:05")
		formatted.WriteString(of.styles.Timestamp.Render(fmt.Sprintf("[%s] ", timestamp)))
	}
	
	// Add icon and message
	formatted.WriteString(style.Render(fmt.Sprintf("%s %s", icon, message)))
	
	return formatted.String()
}

// SetStyles sets the output styles
func (of *OutputFormatter) SetStyles(styles OutputStyles) {
	of.styles = styles
}

// SetConfig sets the output configuration
func (of *OutputFormatter) SetConfig(config OutputConfig) {
	of.config = config
}

// WrapText wraps text to the specified width
func (of *OutputFormatter) WrapText(text string, width int) string {
	if width <= 0 {
		return text
	}
	
	words := strings.Fields(text)
	if len(words) == 0 {
		return text
	}
	
	var wrapped strings.Builder
	lineLength := 0
	
	for _, word := range words {
		wordLen := len(word)
		
		if lineLength+wordLen+1 > width && lineLength > 0 {
			wrapped.WriteString("\n")
			lineLength = 0
		}
		
		if lineLength > 0 {
			wrapped.WriteString(" ")
			lineLength++
		}
		
		wrapped.WriteString(word)
		lineLength += wordLen
	}
	
	return wrapped.String()
}

// ColorPrint provides colored console output using fatih/color
type ColorPrint struct {
	enabled bool
}

// NewColorPrint creates a new color printer
func NewColorPrint(enabled bool) *ColorPrint {
	return &ColorPrint{enabled: enabled}
}

// Success prints a success message in green
func (cp *ColorPrint) Success(format string, args ...interface{}) {
	if cp.enabled {
		color.Green("✓ "+format, args...)
	} else {
		fmt.Printf("✓ "+format+"\n", args...)
	}
}

// Error prints an error message in red
func (cp *ColorPrint) Error(format string, args ...interface{}) {
	if cp.enabled {
		color.Red("✗ "+format, args...)
	} else {
		fmt.Printf("✗ "+format+"\n", args...)
	}
}

// Warning prints a warning message in yellow
func (cp *ColorPrint) Warning(format string, args ...interface{}) {
	if cp.enabled {
		color.Yellow("⚠ "+format, args...)
	} else {
		fmt.Printf("⚠ "+format+"\n", args...)
	}
}

// Info prints an info message in blue
func (cp *ColorPrint) Info(format string, args ...interface{}) {
	if cp.enabled {
		color.Blue("ℹ "+format, args...)
	} else {
		fmt.Printf("ℹ "+format+"\n", args...)
	}
}

// Debug prints a debug message in gray
func (cp *ColorPrint) Debug(format string, args ...interface{}) {
	if cp.enabled {
		color.New(color.FgHiBlack).Printf("🐛 "+format+"\n", args...)
	} else {
		fmt.Printf("🐛 "+format+"\n", args...)
	}
}

// Command prints a command in magenta
func (cp *ColorPrint) Command(format string, args ...interface{}) {
	if cp.enabled {
		color.Magenta("$ "+format, args...)
	} else {
		fmt.Printf("$ "+format+"\n", args...)
	}
}
